package jumbo_v2_event_consumer

import (
	"context"
	"errors"
	"time"

	"github.com/Zomato/flash-gateway/internal/helpers"
	"github.com/Zomato/flash-gateway/internal/models"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/impressionevents"
	jumboEvent "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"github.com/golang/protobuf/ptypes"
)

func getImpressionEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &impressionevents.ImpressionEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to impression event. Error: " + err.Error())
	}

	ingestionTimestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestionTimestamp == 0 {
		ingestionTimestamp = time.Now().Unix()
	}

	// Build products slice
	var products []models.ImpressionEventProduct
	if props := pb.GetProperties(); props != nil {
		if pItems := props.GetProducts(); pItems != nil {
			products = make([]models.ImpressionEventProduct, 0, len(pItems))
			for _, product := range pItems {
				products = append(products, models.ImpressionEventProduct{
					ProductID:      helpers.GetSafeInt64Ptr(product.GetProductId()),
					L0Category:     helpers.GetSafeStringPtr(product.GetL0Category()),
					L1Category:     helpers.GetSafeStringPtr(product.GetL1Category()),
					L2Category:     helpers.GetSafeStringPtr(product.GetL2Category()),
					PType:          helpers.GetSafeStringPtr(product.GetPtype()),
					Name:           helpers.GetSafeStringPtr(product.GetName()),
					Brand:          helpers.GetSafeStringPtr(product.GetBrand()),
					Price:          helpers.GetSafeDoublePtr(product.GetPrice()),
					MRP:            helpers.GetSafeDoublePtr(product.GetMrp()),
					Quantity:       helpers.GetSafeInt32Ptr(product.GetQuantity()),
					TypeID:         helpers.GetSafeInt64Ptr(product.GetTypeId()),
					Currency:       helpers.GetSafeStringPtr(product.GetCurrency()),
					ShipmentID:     helpers.GetSafeStringPtr(product.GetShipmentId()),
					InventoryLimit: helpers.GetSafeInt64Ptr(product.GetInventoryLimit()),
					SBCPrice:       helpers.GetSafeDoublePtr(product.GetSbcPrice()),
				})
			}
		}
	}

	// Build shipments slice
	var shipments []models.ImpressionEventShipments
	if props := pb.GetProperties(); props != nil {
		if sItems := props.GetShipments(); sItems != nil {
			shipments = make([]models.ImpressionEventShipments, 0, len(sItems))
			for _, shipment := range sItems {
				shipments = append(shipments, models.ImpressionEventShipments{
					ShipmentID:     helpers.GetSafeStringPtr(shipment.GetShipmentId()),
					ShipmentType:   helpers.GetSafeStringPtr(shipment.GetShipmentType()),
					SlotAmount:     helpers.GetSafeDoublePtr(shipment.GetSlotAmount()),
					ShipmentValue:  helpers.GetSafeDoublePtr(shipment.GetShipmentValue()),
					SlotDate:       helpers.GetSafeStringPtr(shipment.GetSlotDate()),
					SlotTime:       helpers.GetSafeStringPtr(shipment.GetSlotTime()),
					IsEditShipment: helpers.GetSafeBoolPtr(shipment.GetIsEditShipment()),
					IsEarliestSlot: helpers.GetSafeBoolPtr(shipment.GetIsEarliestSlot()),
				})
			}
		}
	}

	// Build products in shipment slice
	var productsInShipment []models.ProductsInShipment
	if props := pb.GetProperties(); props != nil {
		if pisItems := props.GetProductsInShipment(); pisItems != nil {
			productsInShipment = make([]models.ProductsInShipment, 0, len(pisItems))
			for _, pis := range pisItems {
				productsInShipment = append(productsInShipment, models.ProductsInShipment{
					ProductID: helpers.GetSafeInt64Ptr(pis.GetProductId()),
				})
			}
		}
	}

	// Build product alternatives slice
	var productAlternatives []models.ProductAlternatives
	if props := pb.GetProperties(); props != nil {
		if paItems := props.GetProductAlternativesList(); paItems != nil {
			productAlternatives = make([]models.ProductAlternatives, 0, len(paItems))
			for _, pa := range paItems {
				alternatives := make([]string, 0, len(pa.GetAlternatives()))
				for _, alt := range pa.GetAlternatives() {
					alternatives = append(alternatives, alt.GetValue())
				}
				productAlternatives = append(productAlternatives, models.ProductAlternatives{
					PID:          helpers.GetSafeStringPtr(pa.GetPid()),
					Alternatives: alternatives,
				})
			}
		}
	}

	// Build merchants slice
	var merchants []models.Merchant
	if props := pb.GetProperties(); props != nil {
		if mItems := props.GetMerchants(); mItems != nil {
			merchants = make([]models.Merchant, 0, len(mItems))
			for _, merchant := range mItems {
				productIDs := make([]int64, 0, len(merchant.GetProductIds()))
				for _, pid := range merchant.GetProductIds() {
					productIDs = append(productIDs, pid.GetValue())
				}
				merchants = append(merchants, models.Merchant{
					IsServiceable:        helpers.GetSafeBoolPtr(merchant.GetIsServiceable()),
					MerchantID:           helpers.GetSafeInt32Ptr(merchant.GetMerchantId()),
					ServiceabilityReason: helpers.GetSafeStringPtr(merchant.GetServiceabilityReason()),
					ProductIDs:           productIDs,
					AssortmentTag:        helpers.GetSafeStringPtr(merchant.GetAssortmentTag()),
				})
			}
		}
	}

	// Build OOS products slice
	var oosProducts []models.OOSProduct
	if props := pb.GetProperties(); props != nil {
		if oosItems := props.GetOosProducts(); oosItems != nil {
			oosProducts = make([]models.OOSProduct, 0, len(oosItems))
			for _, oos := range oosItems {
				oosProducts = append(oosProducts, models.OOSProduct{
					Reason: helpers.GetSafeStringPtr(oos.GetReason()),
					ID:     helpers.GetSafeInt64Ptr(oos.GetId()),
				})
			}
		}
	}

	// Convert InputType enum
	var inputType *models.InputType
	if props := pb.GetProperties(); props != nil {
		if it := props.GetInputType(); it != 0 {
			// Handle protobuf enum integer values
			switch it {
			case 1: // AUTO = 1 in protobuf
				convertedType := models.AUTO
				inputType = &convertedType
			case 2: // MANUAL = 2 in protobuf
				convertedType := models.MANUAL
				inputType = &convertedType
			default: // INPUT_TYPE_UNSPECIFIED = 0 in protobuf
				convertedType := models.INPUT_TYPE_UNSPECIFIED
				inputType = &convertedType
			}
		}
	}

	data := models.ImpressionEventData{
		Source:             envelope.GetHeader().GetSource().String(),
		DeviceID:           envelope.GetHeader().GetDeviceId().GetValue(),
		SessionID:          envelope.GetHeader().GetSessionId().GetValue(),
		UserID:             envelope.GetHeader().GetUserId().GetValue(),
		UserAgent:          envelope.GetHeader().GetUserAgent().GetValue(),
		Timestamp:          envelope.GetHeader().GetTimestamp().GetSeconds(),
		Time:               envelope.GetHeader().GetTimestamp().GetSeconds(), // Use timestamp instead of non-existent time field
		IngestionTimestamp: ingestionTimestamp,
		Location:           envelope.GetHeader().GetLocation().GetValue(),
		AppInfo: models.AppInfoData{
			DevicePerformance: envelope.GetHeader().GetAppInfo().GetDevicePerformance().String(),
			Theme:             envelope.GetHeader().GetAppInfo().GetTheme().String(),
			SystemTheme:       envelope.GetHeader().GetAppInfo().GetSystemTheme().String(),
			AppAppearance:     envelope.GetHeader().GetAppInfo().GetAppAppearance().String(),
		},
		EventName: pb.GetEventName().GetValue(),
		Traits: models.ImpressionTraitsData{
			AppFlavor:              helpers.GetSafeStringPtr(pb.GetTraits().GetAppFlavor()),
			CartID:                 helpers.GetSafeStringPtr(pb.GetTraits().GetCartId()),
			ChainID:                helpers.GetSafeInt32Ptr(pb.GetTraits().GetChainId()),
			Channel:                helpers.GetSafeStringPtr(pb.GetTraits().GetChannel()),
			CityID:                 helpers.GetSafeInt32Ptr(pb.GetTraits().GetCityId()),
			CityName:               helpers.GetSafeStringPtr(pb.GetTraits().GetCityName()),
			DeviceUUID:             helpers.GetSafeStringPtr(pb.GetTraits().GetDeviceUuid()),
			InstallCampaign:        helpers.GetSafeStringPtr(pb.GetTraits().GetInstallCampaign()),
			InstallSource:          helpers.GetSafeStringPtr(pb.GetTraits().GetInstallSource()),
			Latitude:               helpers.GetSafeDoublePtr(pb.GetTraits().GetLatitude()),
			LifetimeOrders:         helpers.GetSafeInt32Ptr(pb.GetTraits().GetLifetimeOrders()),
			Longitude:              helpers.GetSafeDoublePtr(pb.GetTraits().GetLongitude()),
			MerchantID:             helpers.GetSafeInt32Ptr(pb.GetTraits().GetMerchantId()),
			MerchantName:           helpers.GetSafeStringPtr(pb.GetTraits().GetMerchantName()),
			MonthlyOrders:          helpers.GetSafeDoublePtr(pb.GetTraits().GetMonthlyOrders()),
			SegmentEnabledFeatures: helpers.GetSafeStringSlicePtr(pb.GetTraits().GetSegmentEnabledFeatures()),
			SessionLaunchSource:    helpers.GetSafeStringPtr(pb.GetTraits().GetSessionLaunchSource()),
			SessionUUID:            helpers.GetSafeStringPtr(pb.GetTraits().GetSessionUuid()),
			TotalOrderValue:        helpers.GetSafeDoublePtr(pb.GetTraits().GetTotalOrderValue()),
			UserType:               helpers.GetSafeStringPtr(pb.GetTraits().GetUserType()),
			AppVersionCode:         helpers.GetSafeInt64Ptr(pb.GetTraits().GetAppVersionCode()),
			UserExperimentBuckets:  helpers.GetSafeStringSlicePtr(pb.GetTraits().GetUserExperimentBuckets()),
			InstallMedium:          helpers.GetSafeStringPtr(pb.GetTraits().GetInstallMedium()),
			InstallReferrer:        helpers.GetSafeStringPtr(pb.GetTraits().GetInstallReferrer()),
			IsDefaultMerchant:      helpers.GetSafeBoolPtr(pb.GetTraits().GetIsDefaultMerchant()),
			TrackingID:             helpers.GetSafeStringPtr(pb.GetTraits().GetTrackingId()),
			AppsflyerAppInstanceID: helpers.GetSafeStringPtr(pb.GetTraits().GetAppsflyerAppInstanceId()),
			LocationHex:            helpers.GetSafeStringPtr(pb.GetTraits().GetLocationHex()),
			HostAppType:            helpers.GetSafeStringPtr(pb.GetTraits().GetHostAppType()),
			HostAppVersion:         helpers.GetSafeStringPtr(pb.GetTraits().GetHostAppVersion()),
			HostAppVersionCode:     helpers.GetSafeInt64Ptr(pb.GetTraits().GetHostAppVersionCode()),
			HostAppUserID:          helpers.GetSafeStringPtr(pb.GetTraits().GetHostAppUserId()),
			SegmentType:            helpers.GetSafeStringSlicePtr(pb.GetTraits().GetSegmentType()),
		},
		Properties: models.ImpressionPropertiesData{
			Products:                      products,
			Shipments:                     shipments,
			ChildWidgetID:                 helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetId()),
			ChildWidgetName:               helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetName()),
			ChildWidgetPosition:           helpers.GetSafeInt32Ptr(pb.GetProperties().GetChildWidgetPosition()),
			ChildWidgetRevisionID:         helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetRevisionId()),
			ChildWidgetTitle:              helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetTitle()),
			ChildWidgetTrackingID:         helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetTrackingId()),
			EntrySourceChildID:            helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceChildId()),
			EntrySourceChildName:          helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceChildName()),
			EntrySourceChildPosition:      helpers.GetSafeInt32Ptr(pb.GetProperties().GetEntrySourceChildPosition()),
			EntrySourceChildTitle:         helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceChildTitle()),
			EntrySourceChildTrackingID:    helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceChildTrackingId()),
			EntrySourceChildVariationID:   helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceChildVariationId()),
			EntrySourceID:                 helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceId()),
			EntrySourceName:               helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceName()),
			EntrySourcePosition:           helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourcePosition()),
			EntrySourceRevisionID:         helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceRevisionId()),
			EntrySourceTitle:              helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceTitle()),
			EntrySourceTrackingID:         helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceTrackingId()),
			EntrySourceVariationID:        helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceVariationId()),
			IsReactPage:                   helpers.GetSafeBoolPtr(pb.GetProperties().GetIsReactPage()),
			LastPageID:                    helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageId()),
			LastPageName:                  helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageName()),
			LastPageRevisionID:            helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageRevisionId()),
			LastPageTitle:                 helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageTitle()),
			LastPageTrackingID:            helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageTrackingId()),
			LastPageVariationID:           helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageVariationId()),
			LastPageVisitID:               helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageVisitId()),
			LastSubPageName:               helpers.GetSafeStringPtr(pb.GetProperties().GetLastSubPageName()),
			LastSubPageTitle:              helpers.GetSafeStringPtr(pb.GetProperties().GetLastSubPageTitle()),
			LastSubPageVisitID:            helpers.GetSafeStringPtr(pb.GetProperties().GetLastSubPageVisitId()),
			PageID:                        helpers.GetSafeStringPtr(pb.GetProperties().GetPageId()),
			PageName:                      helpers.GetSafeStringPtr(pb.GetProperties().GetPageName()),
			PageRevisionID:                helpers.GetSafeStringPtr(pb.GetProperties().GetPageRevisionId()),
			PageTitle:                     helpers.GetSafeStringPtr(pb.GetProperties().GetPageTitle()),
			PageTrackingID:                helpers.GetSafeStringPtr(pb.GetProperties().GetPageTrackingId()),
			PageVariationID:               helpers.GetSafeStringPtr(pb.GetProperties().GetPageVariationId()),
			PageVisitID:                   helpers.GetSafeStringPtr(pb.GetProperties().GetPageVisitId()),
			SubPageName:                   helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageName()),
			SubPageTitle:                  helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageTitle()),
			SubPageVisitID:                helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageVisitId()),
			WidgetTrackingID:              helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetTrackingId()),
			WidgetVariationID:             helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetVariationId()),
			AddressCount:                  helpers.GetSafeInt32Ptr(pb.GetProperties().GetAddressCount()),
			AddressID:                     helpers.GetSafeStringPtr(pb.GetProperties().GetAddressId()),
			AddressType:                   helpers.GetSafeStringPtr(pb.GetProperties().GetAddressType()),
			AdsAssetTypeID:                helpers.GetSafeStringPtr(pb.GetProperties().GetAdsAssetTypeId()),
			AdsCampaignID:                 helpers.GetSafeInt32Ptr(pb.GetProperties().GetAdsCampaignId()),
			AdsCollectionID:               helpers.GetSafeStringPtr(pb.GetProperties().GetAdsCollectionId()),
			AdsSubcampaignID:              helpers.GetSafeInt32Ptr(pb.GetProperties().GetAdsSubcampaignId()),
			AdsType:                       helpers.GetSafeStringPtr(pb.GetProperties().GetAdsType()),
			AerobarID:                     helpers.GetSafeStringPtr(pb.GetProperties().GetAerobarId()),
			Amount:                        helpers.GetSafeDoublePtr(pb.GetProperties().GetAmount()),
			AppInstalled:                  helpers.GetSafeStringPtr(pb.GetProperties().GetAppInstalled()),
			AvailableUpdateVersion:        helpers.GetSafeStringPtr(pb.GetProperties().GetAvailableUpdateVersion()),
			Badge:                         helpers.GetSafeStringPtr(pb.GetProperties().GetBadge()),
			Brand:                         helpers.GetSafeStringPtr(pb.GetProperties().GetBrand()),
			ButtonText:                    helpers.GetSafeStringPtr(pb.GetProperties().GetButtonText()),
			Campaign:                      helpers.GetSafeStringPtr(pb.GetProperties().GetCampaign()),
			CampaignID:                    helpers.GetSafeStringPtr(pb.GetProperties().GetCampaignId()),
			CampaignIdentifier:            helpers.GetSafeStringPtr(pb.GetProperties().GetCampaignIdentifier()),
			CardType:                      helpers.GetSafeStringPtr(pb.GetProperties().GetCardType()),
			CartID:                        helpers.GetSafeStringPtr(pb.GetProperties().GetCartId()),
			CartSavings:                   helpers.GetSafeDoublePtr(pb.GetProperties().GetCartSavings()),
			CartType:                      helpers.GetSafeStringPtr(pb.GetProperties().GetCartType()),
			CartValue:                     helpers.GetSafeDoublePtr(pb.GetProperties().GetCartValue()),
			CheckboxState:                 helpers.GetSafeBoolPtr(pb.GetProperties().GetCheckboxState()),
			ChildWidgetImpressionCount:    helpers.GetSafeInt32Ptr(pb.GetProperties().GetChildWidgetImpressionCount()),
			ClickSource:                   helpers.GetSafeStringPtr(pb.GetProperties().GetClickSource()),
			CollectionID:                  helpers.GetSafeStringPtr(pb.GetProperties().GetCollectionId()),
			CouponAmount:                  helpers.GetSafeDoublePtr(pb.GetProperties().GetCouponAmount()),
			CouponCode:                    helpers.GetSafeStringPtr(pb.GetProperties().GetCouponCode()),
			CouponDiscountValue:           helpers.GetSafeDoublePtr(pb.GetProperties().GetCouponDiscountValue()),
			CouponType:                    helpers.GetSafeStringPtr(pb.GetProperties().GetCouponType()),
			CTAType:                       helpers.GetSafeStringPtr(pb.GetProperties().GetCtaType()),
			CumulativeDelay:               helpers.GetSafeDoublePtr(pb.GetProperties().GetCumulativeDelay()),
			Currency:                      helpers.GetSafeStringPtr(pb.GetProperties().GetCurrency()),
			CurrentCartSavings:            helpers.GetSafeDoublePtr(pb.GetProperties().GetCurrentCartSavings()),
			CurrentObjectID:               helpers.GetSafeStringPtr(pb.GetProperties().GetCurrentObjectId()),
			CustomData:                    helpers.GetSafeStringPtr(pb.GetProperties().GetCustomData()),
			Data1:                         helpers.GetSafeStringPtr(pb.GetProperties().GetData1()),
			Data2:                         helpers.GetSafeStringPtr(pb.GetProperties().GetData2()),
			Data3:                         helpers.GetSafeStringPtr(pb.GetProperties().GetData3()),
			Data4:                         helpers.GetSafeStringPtr(pb.GetProperties().GetData4()),
			Deeplink:                      helpers.GetSafeStringPtr(pb.GetProperties().GetDeeplink()),
			DeviceLat:                     helpers.GetSafeDoublePtr(pb.GetProperties().GetDeviceLat()),
			DeviceLon:                     helpers.GetSafeDoublePtr(pb.GetProperties().GetDeviceLon()),
			DialogType:                    helpers.GetSafeStringPtr(pb.GetProperties().GetDialogType()),
			Enabled:                       helpers.GetSafeBoolPtr(pb.GetProperties().GetEnabled()),
			ErrorDescription:              helpers.GetSafeStringPtr(pb.GetProperties().GetErrorDescription()),
			EventSourceIdentifier:         helpers.GetSafeStringPtr(pb.GetProperties().GetEventSourceIdentifier()),
			FavouriteIconState:            helpers.GetSafeStringPtr(pb.GetProperties().GetFavouriteIconState()),
			FilterKeys:                    helpers.GetSafeStringPtr(pb.GetProperties().GetFilterKeys()),
			FilterPosition:                helpers.GetSafeStringPtr(pb.GetProperties().GetFilterPosition()),
			FiltersPresent:                helpers.GetSafeStringPtr(pb.GetProperties().GetFiltersPresent()),
			Icon:                          helpers.GetSafeStringPtr(pb.GetProperties().GetIcon()),
			ID:                            helpers.GetSafeStringPtr(pb.GetProperties().GetId()),
			ImageURL:                      helpers.GetSafeStringPtr(pb.GetProperties().GetImageUrl()),
			ImagesShown:                   helpers.GetSafeBoolPtr(pb.GetProperties().GetImagesShown()),
			ImagesShownFlags:              helpers.GetSafeStringPtr(pb.GetProperties().GetImagesShownFlags()),
			InvalidIds:                    helpers.GetSafeStringSlicePtr(pb.GetProperties().GetInvalidIds()),
			Inventory:                     helpers.GetSafeInt32Ptr(pb.GetProperties().GetInventory()),
			InventoryLimit:                helpers.GetSafeInt32Ptr(pb.GetProperties().GetInventoryLimit()),
			IsChecked:                     helpers.GetSafeBoolPtr(pb.GetProperties().GetIsChecked()),
			IsCouponApplicable:            helpers.GetSafeStringPtr(pb.GetProperties().GetIsCouponApplicable()),
			IsEarliestSlot:                helpers.GetSafeBoolPtr(pb.GetProperties().GetIsEarliestSlot()),
			ItemsInCart:                   helpers.GetSafeInt32Ptr(pb.GetProperties().GetItemsInCart()),
			L0Category:                    helpers.GetSafeStringPtr(pb.GetProperties().GetL0Category()),
			L1Category:                    helpers.GetSafeStringPtr(pb.GetProperties().GetL1Category()),
			L2Category:                    helpers.GetSafeStringPtr(pb.GetProperties().GetL2Category()),
			Label:                         helpers.GetSafeStringPtr(pb.GetProperties().GetLabel()),
			Latitude:                      helpers.GetSafeDoublePtr(pb.GetProperties().GetLatitude()),
			LocationFetchAccuracy:         helpers.GetSafeStringPtr(pb.GetProperties().GetLocationFetchAccuracy()),
			LocationFetchAltitude:         helpers.GetSafeStringPtr(pb.GetProperties().GetLocationFetchAltitude()),
			LocationUpdateType:            helpers.GetSafeStringPtr(pb.GetProperties().GetLocationUpdateType()),
			Longitude:                     helpers.GetSafeDoublePtr(pb.GetProperties().GetLongitude()),
			MerchantID:                    helpers.GetSafeInt32Ptr(pb.GetProperties().GetMerchantId()),
			MerchantType:                  helpers.GetSafeStringPtr(pb.GetProperties().GetMerchantType()),
			Message:                       helpers.GetSafeStringPtr(pb.GetProperties().GetMessage()),
			MRP:                           helpers.GetSafeInt32Ptr(pb.GetProperties().GetMrp()),
			Name:                          helpers.GetSafeStringPtr(pb.GetProperties().GetName()),
			NewLocality:                   helpers.GetSafeStringPtr(pb.GetProperties().GetNewLocality()),
			NextObjectID:                  helpers.GetSafeStringPtr(pb.GetProperties().GetNextObjectId()),
			NotificationType:              helpers.GetSafeStringPtr(pb.GetProperties().GetNotificationType()),
			NumberOfFiles:                 helpers.GetSafeInt32Ptr(pb.GetProperties().GetNumberOfFiles()),
			NumberOfProducts:              helpers.GetSafeInt32Ptr(pb.GetProperties().GetNumberOfProducts()),
			Offer:                         helpers.GetSafeStringPtr(pb.GetProperties().GetOffer()),
			OfferText:                     helpers.GetSafeBoolPtr(pb.GetProperties().GetOfferText()),
			OldLocality:                   helpers.GetSafeStringPtr(pb.GetProperties().GetOldLocality()),
			OosPidList:                    helpers.GetSafeStringSlicePtr(pb.GetProperties().GetOosPidList()),
			OrderCount:                    helpers.GetSafeInt32Ptr(pb.GetProperties().GetOrderCount()),
			OrderHashID:                   helpers.GetSafeStringPtr(pb.GetProperties().GetOrderHashId()),
			OrderID:                       helpers.GetSafeStringPtr(pb.GetProperties().GetOrderId()),
			OrderState:                    helpers.GetSafeStringPtr(pb.GetProperties().GetOrderState()),
			OrderStatus:                   helpers.GetSafeStringPtr(pb.GetProperties().GetOrderStatus()),
			OverlayBadges:                 helpers.GetSafeStringPtr(pb.GetProperties().GetOverlayBadges()),
			OverlayPresent:                helpers.GetSafeBoolPtr(pb.GetProperties().GetOverlayPresent()),
			PageType:                      helpers.GetSafeStringPtr(pb.GetProperties().GetPageType()),
			Payload:                       helpers.GetSafeStringPtr(pb.GetProperties().GetPayload()),
			PaymentMethod:                 helpers.GetSafeStringPtr(pb.GetProperties().GetPaymentMethod()),
			PaymentMode:                   helpers.GetSafeStringPtr(pb.GetProperties().GetPaymentMode()),
			PopupType:                     helpers.GetSafeStringPtr(pb.GetProperties().GetPopupType()),
			Position:                      helpers.GetSafeInt32Ptr(pb.GetProperties().GetPosition()),
			PostChangeLatitude:            helpers.GetSafeStringPtr(pb.GetProperties().GetPostChangeLatitude()),
			PostChangeLongitude:           helpers.GetSafeStringPtr(pb.GetProperties().GetPostChangeLongitude()),
			PrevLabel:                     helpers.GetSafeStringPtr(pb.GetProperties().GetPrevLabel()),
			Price:                         helpers.GetSafeDoublePtr(pb.GetProperties().GetPrice()),
			ProductAlternativesList:       productAlternatives,
			ProductCount:                  helpers.GetSafeInt32Ptr(pb.GetProperties().GetProductCount()),
			ProductID:                     helpers.GetSafeInt64Ptr(pb.GetProperties().GetProductId()),
			ProductIds:                    helpers.GetSafeStringPtr(pb.GetProperties().GetProductIds()),
			ProductListID:                 helpers.GetSafeStringPtr(pb.GetProperties().GetProductListId()),
			ProductPosition:               helpers.GetSafeInt32Ptr(pb.GetProperties().GetProductPosition()),
			ProductsInShipment:            productsInShipment,
			PromoIdentifers:               helpers.GetSafeStringSlicePtr(pb.GetProperties().GetPromoIdentifers()),
			PType:                         helpers.GetSafeStringPtr(pb.GetProperties().GetPtype()),
			Quantity:                      helpers.GetSafeInt32Ptr(pb.GetProperties().GetQuantity()),
			Rating:                        helpers.GetSafeStringPtr(pb.GetProperties().GetRating()),
			Reason:                        helpers.GetSafeStringPtr(pb.GetProperties().GetReason()),
			RecommendationID:              helpers.GetSafeStringPtr(pb.GetProperties().GetRecommendationId()),
			SBCCartSavings:                helpers.GetSafeDoublePtr(pb.GetProperties().GetSbcCartSavings()),
			SdkVersion:                    helpers.GetSafeStringPtr(pb.GetProperties().GetSdkVersion()),
			SearchActualKeyword:           helpers.GetSafeStringPtr(pb.GetProperties().GetSearchActualKeyword()),
			SearchInputKeyword:            helpers.GetSafeStringPtr(pb.GetProperties().GetSearchInputKeyword()),
			SearchKeywordParent:           helpers.GetSafeStringPtr(pb.GetProperties().GetSearchKeywordParent()),
			SearchKeywordType:             helpers.GetSafeStringPtr(pb.GetProperties().GetSearchKeywordType()),
			SearchPreviousKeyword:         helpers.GetSafeStringPtr(pb.GetProperties().GetSearchPreviousKeyword()),
			SearchResultCount:             helpers.GetSafeInt32Ptr(pb.GetProperties().GetSearchResultCount()),
			SearchUserTypedInputKeyword:   helpers.GetSafeStringPtr(pb.GetProperties().GetSearchUserTypedInputKeyword()),
			Selected:                      helpers.GetSafeBoolPtr(pb.GetProperties().GetSelected()),
			SelectedItems:                 helpers.GetSafeStringPtr(pb.GetProperties().GetSelectedItems()),
			SelectedTab:                   helpers.GetSafeStringPtr(pb.GetProperties().GetSelectedTab()),
			ServiceType:                   helpers.GetSafeStringPtr(pb.GetProperties().GetServiceType()),
			SharedID:                      helpers.GetSafeStringPtr(pb.GetProperties().GetSharedId()),
			ShipmentID:                    helpers.GetSafeStringPtr(pb.GetProperties().GetShipmentId()),
			ShipmentType:                  helpers.GetSafeStringPtr(pb.GetProperties().GetShipmentType()),
			ShipmentValue:                 helpers.GetSafeDoublePtr(pb.GetProperties().GetShipmentValue()),
			Shipping:                      helpers.GetSafeDoublePtr(pb.GetProperties().GetShipping()),
			SlotAmount:                    helpers.GetSafeDoublePtr(pb.GetProperties().GetSlotAmount()),
			SlotDate:                      helpers.GetSafeStringPtr(pb.GetProperties().GetSlotDate()),
			SlotTime:                      helpers.GetSafeStringPtr(pb.GetProperties().GetSlotTime()),
			Source:                        helpers.GetSafeStringPtr(pb.GetProperties().GetSource()),
			SSID:                          helpers.GetSafeStringPtr(pb.GetProperties().GetSsid()),
			State:                         helpers.GetSafeStringPtr(pb.GetProperties().GetState()),
			Status:                        helpers.GetSafeStringPtr(pb.GetProperties().GetStatus()),
			SubPageID:                     helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageId()),
			SubPageRevisionID:             helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageRevisionId()),
			SubcategoryID:                 helpers.GetSafeStringPtr(pb.GetProperties().GetSubcategoryId()),
			Subtitle:                      helpers.GetSafeStringPtr(pb.GetProperties().GetSubtitle()),
			SubtitleTag:                   helpers.GetSafeStringPtr(pb.GetProperties().GetSubtitleTag()),
			SuggestedKeywords:             helpers.GetSafeStringPtr(pb.GetProperties().GetSuggestedKeywords()),
			SuggestionPosition:            helpers.GetSafeStringPtr(pb.GetProperties().GetSuggestionPosition()),
			SuggestionSource:              helpers.GetSafeStringPtr(pb.GetProperties().GetSuggestionSource()),
			SuggestionType:                helpers.GetSafeStringPtr(pb.GetProperties().GetSuggestionType()),
			SuggestionValue:               helpers.GetSafeStringPtr(pb.GetProperties().GetSuggestionValue()),
			SwipeDirection:                helpers.GetSafeStringPtr(pb.GetProperties().GetSwipeDirection()),
			TimeToDeliveryInMins:          helpers.GetSafeDoublePtr(pb.GetProperties().GetTimeToDeliveryInMins()),
			TipAmount:                     helpers.GetSafeDoublePtr(pb.GetProperties().GetTipAmount()),
			Title:                         helpers.GetSafeStringPtr(pb.GetProperties().GetTitle()),
			Total:                         helpers.GetSafeDoublePtr(pb.GetProperties().GetTotal()),
			TotalItems:                    helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalItems()),
			TotalProductsInCart:           helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalProductsInCart()),
			TotalProductsInSharedCart:     helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalProductsInSharedCart()),
			TotalProductsInShipment:       helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalProductsInShipment()),
			TotalProductsOutOfStock:       helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalProductsOutOfStock()),
			TotalShipmentsInCart:          helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalShipmentsInCart()),
			Type:                          helpers.GetSafeStringPtr(pb.GetProperties().GetType()),
			UniqueProducts:                helpers.GetSafeInt32Ptr(pb.GetProperties().GetUniqueProducts()),
			UniqueProductsInCart:          helpers.GetSafeInt32Ptr(pb.GetProperties().GetUniqueProductsInCart()),
			UniqueProductsInShipment:      helpers.GetSafeInt32Ptr(pb.GetProperties().GetUniqueProductsInShipment()),
			UniqueProductsOutOfStock:      helpers.GetSafeInt32Ptr(pb.GetProperties().GetUniqueProductsOutOfStock()),
			URL:                           helpers.GetSafeStringPtr(pb.GetProperties().GetUrl()),
			WidgetID:                      helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetId()),
			WidgetImpressionCount:         helpers.GetSafeInt32Ptr(pb.GetProperties().GetWidgetImpressionCount()),
			WidgetName:                    helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetName()),
			WidgetPosition:                helpers.GetSafeInt32Ptr(pb.GetProperties().GetWidgetPosition()),
			WidgetRevisionID:              helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetRevisionId()),
			WidgetTitle:                   helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetTitle()),
			WidgetType:                    helpers.GetSafeInt32Ptr(pb.GetProperties().GetWidgetType()),
			AdsCostID:                     helpers.GetSafeInt32Ptr(pb.GetProperties().GetAdsCostId()),
			Merchants:                     merchants,
			OOSProducts:                   oosProducts,
			OfferCode:                     helpers.GetSafeStringPtr(pb.GetProperties().GetOfferCode()),
			SectionTrackingID:             helpers.GetSafeStringPtr(pb.GetProperties().GetSectionTrackingId()),
			NextAvailableAt:               helpers.GetSafeStringPtr(pb.GetProperties().GetNextAvailableAt()),
			IsDonationGiven:               helpers.GetSafeBoolPtr(pb.GetProperties().GetIsDonationGiven()),
			ProductState:                  helpers.GetSafeStringPtr(pb.GetProperties().GetProductState()),
			LastSubPageID:                 helpers.GetSafeStringPtr(pb.GetProperties().GetLastSubPageId()),
			WatchedDuration:               helpers.GetSafeDoublePtr(pb.GetProperties().GetWatchedDuration()),
			Duration:                      helpers.GetSafeDoublePtr(pb.GetProperties().GetDuration()),
			IsSurpriseOrder:               helpers.GetSafeBoolPtr(pb.GetProperties().GetIsSurpriseOrder()),
			IsItemsHidden:                 helpers.GetSafeBoolPtr(pb.GetProperties().GetIsItemsHidden()),
			HighlightIDs:                  helpers.GetSafeStringPtr(pb.GetProperties().GetHighlightIds()),
			WidgetGroupTrackingID:         helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetGroupTrackingId()),
			EntrySourceGroupTrackingID:    helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceGroupTrackingId()),
			VideoShown:                    helpers.GetSafeBoolPtr(pb.GetProperties().GetVideoShown()),
			IsOtpDetected:                 helpers.GetSafeBoolPtr(pb.GetProperties().GetIsOtpDetected()),
			InputType:                     inputType,
			BirthDate:                     helpers.GetSafeStringPtr(pb.GetProperties().GetBirthDate()),
			Preference:                    helpers.GetSafeStringPtr(pb.GetProperties().GetPreference()),
			IsBottomSheetCurrentlyShown:   helpers.GetSafeBoolPtr(pb.GetProperties().GetIsBottomSheetCurrentlyShown()),
			HasBottomSheetBeenShownBefore: helpers.GetSafeBoolPtr(pb.GetProperties().GetHasBottomSheetBeenShownBefore()),
			IsAddressSelectedManually:     helpers.GetSafeBoolPtr(pb.GetProperties().GetIsAddressSelectedManually()),
			Distance:                      helpers.GetSafeInt32Ptr(pb.GetProperties().GetDistance()),
			CouponID:                      helpers.GetSafeStringPtr(pb.GetProperties().GetCouponId()),
			ErrorType:                     helpers.GetSafeStringPtr(pb.GetProperties().GetErrorType()),
			ErrorMessage:                  helpers.GetSafeStringPtr(pb.GetProperties().GetErrorMessage()),
			EtaIdentifier:                 helpers.GetSafeStringPtr(pb.GetProperties().GetEtaIdentifier()),
			WishlistAdded:                 helpers.GetSafeBoolPtr(pb.GetProperties().GetWishlistAdded()),
			LoginMethod:                   helpers.GetSafeStringPtr(pb.GetProperties().GetLoginMethod()),
			IsLowPowerMode:                helpers.GetSafeBoolPtr(pb.GetProperties().GetIsLowPowerMode()),
			IsAccessibilityEnabled:        helpers.GetSafeBoolPtr(pb.GetProperties().GetIsAccessibilityEnabled()),
		},
		URL:            envelope.Url,
		EventID:        envelope.EventId,
		SequenceID:     envelope.SequenceId,
		SequenceOffset: int64(envelope.SequenceOffset),
	}

	impressionEventString, err := json.MarshalToString(data)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(impressionEventString),
		Key:     key,
		Table:   envelope.GetTable(),
	}}, err
}
