package jumbo_v2_event_consumer

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/marketing/blinkitdeliverypartnerappsflyerevents"

	"github.com/Zomato/flash-gateway/internal/constants"
	"github.com/Zomato/flash-gateway/internal/helpers"
	"github.com/Zomato/flash-gateway/internal/models"
	"github.com/Zomato/flash-gateway/internal/transformers"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/bistroclickevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/bistroorderfeedbackevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/blinkitwebevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/clickevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/productimageshownevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/kitchenops/suborderitemprepdetails"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/storeops/storeopsappevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehouseempactivitytrackerevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehouseorderevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehouseoutboundcontainerevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehousepackagingactivityevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehousepicklistevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehouseplbvevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehouseputlistevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehousesegregationevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/warehouse/warehouseunloadingtaskevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/district/eventsbussinesstracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/district/eventsticket"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/district/homepagetracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/district/moviesevents"
	jumboEvent "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/ads/adsstudiotracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/ads/jadtracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/apigateway/o2carttracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/corporatefundsservice/zfelimittransactionevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/dining/diningservice/diningtrtransactionevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/gamification/delightservice/videomessageevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/hyperpure/hyperpureorderevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/karma/karmatracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/liveorderservice/liveorderserviceevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/logistics/driverwalletbreachstatusevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/logistics/hermes/hermescellspeedstats"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/logistics/hermes/hermesdrivermatchstats"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/logistics/logisticsaccountingservice/logisticsaccountingeventledger"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/logistics/xtremeorders"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/marketing/appsflyerevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/merchant/merchantrelayevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/merchant/mxoutletsnapshotevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/onesupport/chatagentmetrics"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/onesupport/onesupportevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/onesupport/unifiedchatevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/onesupport/unifiedticketevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/platform/apperrormetrics"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/platform/appperformancemetrics"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/platform/apprequestmetrics"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/platform/webrequestmetrics"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/promos/promoevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/search/serviceability"
	resServiceability "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/serviceability"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/serviceability/celllogsevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/serviceability/serviceabilitylocalitystats"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/time/ddtsaliencetracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/timeservice/etatracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/timeservice/orderrushevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/weather/weatherstationevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/web/adscentralwebtracking"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/web/pageview"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/web/webviewstracking"
	"github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/event"
	lms "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/logistics/lead_management_service"
	"github.com/golang/protobuf/ptypes"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

var validLiveOrderServiceEvents = map[string]struct{}{
	"O2CrystalCarouselImpression":                     {},
	"O2CrystalVideoBannerImpression":                  {},
	"O2CrystalBottomBannerImpression":                 {},
	"O2CrystalMastheadStaticImpression":               {},
	"O2CrystalMastheadImageImpression":                {},
	"O2CrystalMastheadShoppableStoriesImpression":     {},
	"O2CrystalMastheadVideoImpression":                {},
	"O2CrystalResMastheadVideoImpression":             {},
	"O2CrystalMastheadScratchCardImpression":          {},
	"O2CrystalMastheadStaticTapped":                   {},
	"O2CrystalMastheadImageTapped":                    {},
	"O2CrystalMastheadShoppableStoriesTapped":         {},
	"O2CrystalMastheadVideoTapped":                    {},
	"O2CrystalMastheadShoppableStoriesItemImpression": {},
	"O2CrystalMapTimelineOrderBannerImpression":       {},
	"O2CrystalMapFixedBannerImpression":               {},
	"O2CrystalMastheadOverlayAnimationTap":            {},
	"O2CrystalBlockerImpression":                      {},
}

type CustomHandlingMap map[string]func(context.Context, *jumboEvent.JumboEvent, []byte) ([]*models.JumboV2TransformedEvent, error)

var ZomatoCustomHandlingV2Map = CustomHandlingMap{
	"karma_tracking":                    getKarmaTrackingString,
	"merchant_relay_events":             getMerchantRelayEvents,
	"live_order_service_events":         getLiveOrderServiceEvents,
	"app_performance_metrics":           getAppPerformanceMetrics,
	"serviceability_locality_stats":     getServiceabilityLocalityStats,
	"ads_central_web_tracking":          getAdsCentralWebTracking,
	"webviews_tracking":                 getWebviewsTracking,
	"ads_studio_tracking":               getAdsStudioTracking,
	"hyperpure_order_events":            getHyperpureOrderEvents,
	"appsflyer_events":                  getDriverObAppsflyerEvent,
	"pageview":                          getPageViewEvents,
	"eta_tracking":                      getEtaTracking,
	"o2_cart_tracking":                  getO2CartTrackingEvents,
	"promo_events":                      getPromoEvents,
	"search_serviceability":             getSearchServiceabilityEvents,
	"ddt_salience_tracking":             getDdtSalienceTrackingEvents,
	"mx_outlet_snapshot_events":         getEventsResStatusUpdateEvents,
	"jadtracking":                       getJAdTrackingEvent,
	"logistics_accounting_event_ledger": getLogisticsAccountingEventLedger,
}

var DistrictCustomHandlingV2Map = CustomHandlingMap{
	"jadtracking":               getDistrictAdsEvent,
	"movies_events":             getMoviesEvents,
	"events_ticket":             getDistrictEventsTicketEvents,
	"events_bussiness_tracking": getEventsBusinessTrackingEvents,
	"homepage_tracking":         getDistrictHomepageTrackingEvents,
	"app_performance_metrics":   getAppPerformanceMetrics,
}

var BlinkitCustomHandlingV2Map = CustomHandlingMap{
	"blinkit_delivery_partner_appsflyer_events": getDriverObBlinkitDeliveryPartnerAppsflyerEvents,
	"product_image_shown_events":                getProductImageShownEvents,
	"impression_events":                         getImpressionEvents,
}

func getProductImageShownEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &productimageshownevents.ProductImageShownEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to product image shown event. Error: " + err.Error())
	}

	ingestionTimestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestionTimestamp == 0 {
		ingestionTimestamp = time.Now().Unix()
	}

	// Build products and shipments slices
	var products []models.Product
	if props := pb.GetProperties(); props != nil {
		if pItems := props.GetProducts(); pItems != nil {
			products = make([]models.Product, 0, len(pItems))
			for _, pr := range pItems {
				if pr == nil {
					continue
				}
				products = append(products, models.Product{
					ProductID:      helpers.GetSafeInt64Ptr(pr.GetProductId()),
					L0Category:     helpers.GetSafeStringPtr(pr.GetL0Category()),
					L1Category:     helpers.GetSafeStringPtr(pr.GetL1Category()),
					L2Category:     helpers.GetSafeStringPtr(pr.GetL2Category()),
					PType:          helpers.GetSafeStringPtr(pr.GetPtype()),
					Name:           helpers.GetSafeStringPtr(pr.GetName()),
					Brand:          helpers.GetSafeStringPtr(pr.GetBrand()),
					Price:          helpers.GetSafeDoublePtr(pr.GetPrice()),
					MRP:            helpers.GetSafeDoublePtr(pr.GetMrp()),
					Quantity:       helpers.GetSafeInt32Ptr(pr.GetQuantity()),
					TypeID:         helpers.GetSafeInt64Ptr(pr.GetTypeId()),
					Currency:       helpers.GetSafeStringPtr(pr.GetCurrency()),
					ShipmentID:     helpers.GetSafeStringPtr(pr.GetShipmentId()),
					InventoryLimit: helpers.GetSafeInt64Ptr(pr.GetInventoryLimit()),
					SBCPrice:       helpers.GetSafeDoublePtr(pr.GetSbcPrice()),
				})
			}
		}
	}

	var shipments []models.Shipment
	if props := pb.GetProperties(); props != nil {
		if sItems := props.GetShipments(); sItems != nil {
			shipments = make([]models.Shipment, 0, len(sItems))
			for _, sh := range sItems {
				if sh == nil {
					continue
				}
				shipments = append(shipments, models.Shipment{
					ShipmentID:     helpers.GetSafeStringPtr(sh.GetShipmentId()),
					ShipmentType:   helpers.GetSafeStringPtr(sh.GetShipmentType()),
					SlotAmount:     helpers.GetSafeDoublePtr(sh.GetSlotAmount()),
					ShipmentValue:  helpers.GetSafeDoublePtr(sh.GetShipmentValue()),
					SlotDate:       helpers.GetSafeStringPtr(sh.GetSlotDate()),
					SlotTime:       helpers.GetSafeStringPtr(sh.GetSlotTime()),
					IsEditShipment: helpers.GetSafeBoolPtr(sh.GetIsEditShipment()),
					IsEarliestSlot: helpers.GetSafeBoolPtr(sh.GetIsEarliestSlot()),
				})
			}
		}
	}

	// Build structured data using omitempty-compatible struct
	data := models.ProductImageShownEventData{
		Source:             envelope.GetHeader().GetSource().String(),
		DeviceID:           envelope.GetHeader().GetDeviceId().GetValue(),
		SessionID:          envelope.GetHeader().GetSessionId().GetValue(),
		UserID:             envelope.GetHeader().GetUserId().GetValue(),
		UserAgent:          envelope.GetHeader().GetUserAgent().GetValue(),
		Timestamp:          envelope.GetHeader().GetTimestamp().GetSeconds(),
		Time:               envelope.GetHeader().GetTimestamp().GetSeconds(),
		IngestionTimestamp: ingestionTimestamp,
		Location:           envelope.GetHeader().GetLocation().GetValue(),
		AppInfo: models.AppInfoData{
			DevicePerformance: envelope.GetHeader().GetAppInfo().GetDevicePerformance().String(),
			Theme:             envelope.GetHeader().GetAppInfo().GetTheme().String(),
			SystemTheme:       envelope.GetHeader().GetAppInfo().GetSystemTheme().String(),
			AppAppearance:     envelope.GetHeader().GetAppInfo().GetAppAppearance().String(),
		},
		EventName: pb.GetEventName().GetValue(),
		Traits: models.ProductImageShownTraitsData{
			AppFlavor:              helpers.GetSafeStringPtr(pb.GetTraits().GetAppFlavor()),
			CartID:                 helpers.GetSafeStringPtr(pb.GetTraits().GetCartId()),
			ChainID:                helpers.GetSafeInt32Ptr(pb.GetTraits().GetChainId()),
			Channel:                helpers.GetSafeStringPtr(pb.GetTraits().GetChannel()),
			CityID:                 helpers.GetSafeInt32Ptr(pb.GetTraits().GetCityId()),
			CityName:               helpers.GetSafeStringPtr(pb.GetTraits().GetCityName()),
			DeviceUUID:             helpers.GetSafeStringPtr(pb.GetTraits().GetDeviceUuid()),
			InstallCampaign:        helpers.GetSafeStringPtr(pb.GetTraits().GetInstallCampaign()),
			InstallSource:          helpers.GetSafeStringPtr(pb.GetTraits().GetInstallSource()),
			Latitude:               helpers.GetSafeDoublePtr(pb.GetTraits().GetLatitude()),
			LifetimeOrders:         helpers.GetSafeInt32Ptr(pb.GetTraits().GetLifetimeOrders()),
			Longitude:              helpers.GetSafeDoublePtr(pb.GetTraits().GetLongitude()),
			MerchantID:             helpers.GetSafeInt32Ptr(pb.GetTraits().GetMerchantId()),
			MerchantName:           helpers.GetSafeStringPtr(pb.GetTraits().GetMerchantName()),
			MonthlyOrders:          helpers.GetSafeDoublePtr(pb.GetTraits().GetMonthlyOrders()),
			SegmentEnabledFeatures: helpers.GetSafeStringSlicePtr(pb.GetTraits().GetSegmentEnabledFeatures()),
			SessionLaunchSource:    helpers.GetSafeStringPtr(pb.GetTraits().GetSessionLaunchSource()),
			SessionUUID:            helpers.GetSafeStringPtr(pb.GetTraits().GetSessionUuid()),
			TotalOrderValue:        helpers.GetSafeDoublePtr(pb.GetTraits().GetTotalOrderValue()),
			UserType:               helpers.GetSafeStringPtr(pb.GetTraits().GetUserType()),
			AppVersionCode:         helpers.GetSafeInt64Ptr(pb.GetTraits().GetAppVersionCode()),
			UserExperimentBuckets:  helpers.GetSafeStringSlicePtr(pb.GetTraits().GetUserExperimentBuckets()),
			InstallMedium:          helpers.GetSafeStringPtr(pb.GetTraits().GetInstallMedium()),
			InstallReferrer:        helpers.GetSafeStringPtr(pb.GetTraits().GetInstallReferrer()),
			IsDefaultMerchant:      helpers.GetSafeBoolPtr(pb.GetTraits().GetIsDefaultMerchant()),
			TrackingID:             helpers.GetSafeStringPtr(pb.GetTraits().GetTrackingId()),
			AppsflyerAppInstanceID: helpers.GetSafeStringPtr(pb.GetTraits().GetAppsflyerAppInstanceId()),
			FirebaseAppInstanceID:  helpers.GetSafeStringPtr(pb.GetTraits().GetFirebaseAppInstanceId()),
			LocationHex:            helpers.GetSafeStringPtr(pb.GetTraits().GetLocationHex()),
			HostAppType:            helpers.GetSafeStringPtr(pb.GetTraits().GetHostAppType()),
			HostAppVersion:         helpers.GetSafeStringPtr(pb.GetTraits().GetHostAppVersion()),
			HostAppVersionCode:     helpers.GetSafeInt64Ptr(pb.GetTraits().GetHostAppVersionCode()),
			HostAppUserID:          helpers.GetSafeStringPtr(pb.GetTraits().GetHostAppUserId()),
			SegmentType:            helpers.GetSafeStringSlicePtr(pb.GetTraits().GetSegmentType()),
		},
		Properties: models.ProductImageShownPropertiesData{
			Products:  products,
			Shipments: shipments,
			// Widget and Page related
			ChildWidgetID:          helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetId()),
			ChildWidgetName:        helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetName()),
			ChildWidgetPosition:    helpers.GetSafeInt32Ptr(pb.GetProperties().GetChildWidgetPosition()),
			ChildWidgetRevisionID:  helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetRevisionId()),
			ChildWidgetTitle:       helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetTitle()),
			ChildWidgetTrackingID:  helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetTrackingId()),
			ChildWidgetVariationID: helpers.GetSafeStringPtr(pb.GetProperties().GetChildWidgetVariationId()),
			EntrySourceTitle:       helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceTitle()),
			IsReactPage:            helpers.GetSafeBoolPtr(pb.GetProperties().GetIsReactPage()),
			LastPageID:             helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageId()),
			LastPageName:           helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageName()),
			LastPageTitle:          helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageTitle()),
			LastPageVisitID:        helpers.GetSafeStringPtr(pb.GetProperties().GetLastPageVisitId()),
			LastSubPageName:        helpers.GetSafeStringPtr(pb.GetProperties().GetLastSubPageName()),
			LastSubPageTitle:       helpers.GetSafeStringPtr(pb.GetProperties().GetLastSubPageTitle()),
			LastSubPageVisitID:     helpers.GetSafeStringPtr(pb.GetProperties().GetLastSubPageVisitId()),
			PageID:                 helpers.GetSafeStringPtr(pb.GetProperties().GetPageId()),
			PageName:               helpers.GetSafeStringPtr(pb.GetProperties().GetPageName()),
			PageRevisionID:         helpers.GetSafeStringPtr(pb.GetProperties().GetPageRevisionId()),
			PageTitle:              helpers.GetSafeStringPtr(pb.GetProperties().GetPageTitle()),
			PageTrackingID:         helpers.GetSafeStringPtr(pb.GetProperties().GetPageTrackingId()),
			PageVariationID:        helpers.GetSafeStringPtr(pb.GetProperties().GetPageVariationId()),
			PageVisitID:            helpers.GetSafeStringPtr(pb.GetProperties().GetPageVisitId()),
			SubPageName:            helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageName()),
			SubPageTitle:           helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageTitle()),
			SubPageVisitID:         helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageVisitId()),
			WidgetTrackingID:       helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetTrackingId()),
			WidgetVariationID:      helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetVariationId()),

			// Product and Business related
			AddressCount:                helpers.GetSafeInt32Ptr(pb.GetProperties().GetAddressCount()),
			AddressID:                   helpers.GetSafeStringPtr(pb.GetProperties().GetAddressId()),
			AddressType:                 helpers.GetSafeStringPtr(pb.GetProperties().GetAddressType()),
			AdsAssetTypeID:              helpers.GetSafeStringPtr(pb.GetProperties().GetAdsAssetTypeId()),
			AdsCampaignID:               helpers.GetSafeInt32Ptr(pb.GetProperties().GetAdsCampaignId()),
			AdsCollectionID:             helpers.GetSafeStringPtr(pb.GetProperties().GetAdsCollectionId()),
			AdsSubcampaignID:            helpers.GetSafeInt32Ptr(pb.GetProperties().GetAdsSubcampaignId()),
			AdsType:                     helpers.GetSafeStringPtr(pb.GetProperties().GetAdsType()),
			Amount:                      helpers.GetSafeDoublePtr(pb.GetProperties().GetAmount()),
			Badge:                       helpers.GetSafeStringPtr(pb.GetProperties().GetBadge()),
			Brand:                       helpers.GetSafeStringPtr(pb.GetProperties().GetBrand()),
			Campaign:                    helpers.GetSafeStringPtr(pb.GetProperties().GetCampaign()),
			CampaignID:                  helpers.GetSafeStringPtr(pb.GetProperties().GetCampaignId()),
			CampaignIdentifier:          helpers.GetSafeStringPtr(pb.GetProperties().GetCampaignIdentifier()),
			CardType:                    helpers.GetSafeStringPtr(pb.GetProperties().GetCardType()),
			CartID:                      helpers.GetSafeStringPtr(pb.GetProperties().GetCartId()),
			CartType:                    helpers.GetSafeStringPtr(pb.GetProperties().GetCartType()),
			CartValue:                   helpers.GetSafeDoublePtr(pb.GetProperties().GetCartValue()),
			CollectionID:                helpers.GetSafeStringPtr(pb.GetProperties().GetCollectionId()),
			CTAType:                     helpers.GetSafeStringPtr(pb.GetProperties().GetCtaType()),
			CumulativeDelay:             helpers.GetSafeDoublePtr(pb.GetProperties().GetCumulativeDelay()),
			Currency:                    helpers.GetSafeStringPtr(pb.GetProperties().GetCurrency()),
			CurrentCartSavings:          helpers.GetSafeDoublePtr(pb.GetProperties().GetCurrentCartSavings()),
			CustomData:                  helpers.GetSafeStringPtr(pb.GetProperties().GetCustomData()),
			Deeplink:                    helpers.GetSafeStringPtr(pb.GetProperties().GetDeeplink()),
			DeviceLat:                   helpers.GetSafeDoublePtr(pb.GetProperties().GetDeviceLat()),
			DeviceLon:                   helpers.GetSafeDoublePtr(pb.GetProperties().GetDeviceLon()),
			Enabled:                     helpers.GetSafeBoolPtr(pb.GetProperties().GetEnabled()),
			EventSourceIdentifier:       helpers.GetSafeStringPtr(pb.GetProperties().GetEventSourceIdentifier()),
			FavouriteIconState:          helpers.GetSafeStringPtr(pb.GetProperties().GetFavouriteIconState()),
			FewLeftBadge:                helpers.GetSafeStringPtr(pb.GetProperties().GetFewLeftBadge()),
			FilterKeys:                  helpers.GetSafeStringPtr(pb.GetProperties().GetFilterKeys()),
			FiltersPresent:              helpers.GetSafeStringPtr(pb.GetProperties().GetFiltersPresent()),
			ID:                          helpers.GetSafeStringPtr(pb.GetProperties().GetId()),
			ImageURL:                    helpers.GetSafeStringPtr(pb.GetProperties().GetImageUrl()),
			ImagesShown:                 helpers.GetSafeBoolPtr(pb.GetProperties().GetImagesShown()),
			ImagesShownFlags:            helpers.GetSafeStringPtr(pb.GetProperties().GetImagesShownFlags()),
			InvalidIds:                  helpers.GetSafeStringSlicePtr(pb.GetProperties().GetInvalidIds()),
			Inventory:                   helpers.GetSafeInt32Ptr(pb.GetProperties().GetInventory()),
			InventoryLimit:              helpers.GetSafeInt32Ptr(pb.GetProperties().GetInventoryLimit()),
			IsOffer:                     helpers.GetSafeBoolPtr(pb.GetProperties().GetIsOffer()),
			ItemsInCart:                 helpers.GetSafeInt32Ptr(pb.GetProperties().GetItemsInCart()),
			L0Category:                  helpers.GetSafeStringPtr(pb.GetProperties().GetL0Category()),
			L1Category:                  helpers.GetSafeStringPtr(pb.GetProperties().GetL1Category()),
			L2Category:                  helpers.GetSafeStringPtr(pb.GetProperties().GetL2Category()),
			Label:                       helpers.GetSafeStringPtr(pb.GetProperties().GetLabel()),
			Latitude:                    helpers.GetSafeDoublePtr(pb.GetProperties().GetLatitude()),
			Longitude:                   helpers.GetSafeDoublePtr(pb.GetProperties().GetLongitude()),
			MerchantID:                  helpers.GetSafeInt32Ptr(pb.GetProperties().GetMerchantId()),
			MerchantType:                helpers.GetSafeStringPtr(pb.GetProperties().GetMerchantType()),
			Message:                     helpers.GetSafeStringPtr(pb.GetProperties().GetMessage()),
			MRP:                         helpers.GetSafeInt32Ptr(pb.GetProperties().GetMrp()),
			Name:                        helpers.GetSafeStringPtr(pb.GetProperties().GetName()),
			Offer:                       helpers.GetSafeStringPtr(pb.GetProperties().GetOffer()),
			OfferText:                   helpers.GetSafeBoolPtr(pb.GetProperties().GetOfferText()),
			OrderID:                     helpers.GetSafeStringPtr(pb.GetProperties().GetOrderId()),
			OrderState:                  helpers.GetSafeStringPtr(pb.GetProperties().GetOrderState()),
			OverlayBadges:               helpers.GetSafeStringPtr(pb.GetProperties().GetOverlayBadges()),
			PageType:                    helpers.GetSafeStringPtr(pb.GetProperties().GetPageType()),
			ParentProduct:               helpers.GetSafeStringPtr(pb.GetProperties().GetParentProduct()),
			PaymentMode:                 helpers.GetSafeStringPtr(pb.GetProperties().GetPaymentMode()),
			Position:                    helpers.GetSafeInt32Ptr(pb.GetProperties().GetPosition()),
			Price:                       helpers.GetSafeDoublePtr(pb.GetProperties().GetPrice()),
			ProductCount:                helpers.GetSafeInt32Ptr(pb.GetProperties().GetProductCount()),
			ProductID:                   helpers.GetSafeInt64Ptr(pb.GetProperties().GetProductId()),
			ProductIds:                  helpers.GetSafeStringPtr(pb.GetProperties().GetProductIds()),
			ProductListID:               helpers.GetSafeStringPtr(pb.GetProperties().GetProductListId()),
			ProductOffers:               helpers.GetSafeStringPtr(pb.GetProperties().GetProductOffers()),
			ProductPosition:             helpers.GetSafeInt32Ptr(pb.GetProperties().GetProductPosition()),
			PromoIdentifers:             helpers.GetSafeStringSlicePtr(pb.GetProperties().GetPromoIdentifers()),
			PType:                       helpers.GetSafeStringPtr(pb.GetProperties().GetPtype()),
			Quantity:                    helpers.GetSafeInt32Ptr(pb.GetProperties().GetQuantity()),
			Rating:                      helpers.GetSafeStringPtr(pb.GetProperties().GetRating()),
			Reason:                      helpers.GetSafeStringPtr(pb.GetProperties().GetReason()),
			RecommendationID:            helpers.GetSafeStringPtr(pb.GetProperties().GetRecommendationId()),
			SBCPrice:                    helpers.GetSafeInt32Ptr(pb.GetProperties().GetSbcPrice()),
			SearchActualKeyword:         helpers.GetSafeStringPtr(pb.GetProperties().GetSearchActualKeyword()),
			SearchInputKeyword:          helpers.GetSafeStringPtr(pb.GetProperties().GetSearchInputKeyword()),
			SearchKeywordParent:         helpers.GetSafeStringPtr(pb.GetProperties().GetSearchKeywordParent()),
			SearchKeywordType:           helpers.GetSafeStringPtr(pb.GetProperties().GetSearchKeywordType()),
			SearchPreviousKeyword:       helpers.GetSafeStringPtr(pb.GetProperties().GetSearchPreviousKeyword()),
			SearchResultCount:           helpers.GetSafeInt32Ptr(pb.GetProperties().GetSearchResultCount()),
			SearchUserTypedInputKeyword: helpers.GetSafeStringPtr(pb.GetProperties().GetSearchUserTypedInputKeyword()),
			SelectedTab:                 helpers.GetSafeStringPtr(pb.GetProperties().GetSelectedTab()),
			SelectedTabPosition:         helpers.GetSafeInt32Ptr(pb.GetProperties().GetSelectedTabPosition()),
			SharedID:                    helpers.GetSafeStringPtr(pb.GetProperties().GetSharedId()),
			Shipping:                    helpers.GetSafeDoublePtr(pb.GetProperties().GetShipping()),
			Source:                      helpers.GetSafeStringPtr(pb.GetProperties().GetSource()),
			State:                       helpers.GetSafeStringPtr(pb.GetProperties().GetState()),
			SubPageID:                   helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageId()),
			SubPageRevisionID:           helpers.GetSafeStringPtr(pb.GetProperties().GetSubPageRevisionId()),
			SubcategoryID:               helpers.GetSafeStringPtr(pb.GetProperties().GetSubcategoryId()),
			Subtitle:                    helpers.GetSafeStringPtr(pb.GetProperties().GetSubtitle()),
			SuggestedKeywords:           helpers.GetSafeStringPtr(pb.GetProperties().GetSuggestedKeywords()),
			TimeToDeliveryInMins:        helpers.GetSafeDoublePtr(pb.GetProperties().GetTimeToDeliveryInMins()),
			Title:                       helpers.GetSafeStringPtr(pb.GetProperties().GetTitle()),
			TotalProductsInCart:         helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalProductsInCart()),
			TotalProductsInSharedCart:   helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalProductsInSharedCart()),
			TotalShipmentsInCart:        helpers.GetSafeInt32Ptr(pb.GetProperties().GetTotalShipmentsInCart()),
			Type:                        helpers.GetSafeStringPtr(pb.GetProperties().GetType()),
			UniqueProductsInCart:        helpers.GetSafeInt32Ptr(pb.GetProperties().GetUniqueProductsInCart()),
			URL:                         helpers.GetSafeStringPtr(pb.GetProperties().GetUrl()),
			WidgetID:                    helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetId()),
			WidgetImpressionCount:       helpers.GetSafeInt32Ptr(pb.GetProperties().GetWidgetImpressionCount()),
			WidgetName:                  helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetName()),
			WidgetPosition:              helpers.GetSafeInt32Ptr(pb.GetProperties().GetWidgetPosition()),
			WidgetRevisionID:            helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetRevisionId()),
			WidgetTitle:                 helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetTitle()),
			AdsCostID:                   helpers.GetSafeInt32Ptr(pb.GetProperties().GetAdsCostId()),
			ProductCTAType:              helpers.GetSafeStringPtr(pb.GetProperties().GetProductCtaType()),
			SectionTrackingID:           helpers.GetSafeStringPtr(pb.GetProperties().GetSectionTrackingId()),
			NextAvailableAt:             helpers.GetSafeStringPtr(pb.GetProperties().GetNextAvailableAt()),
			ProductState:                helpers.GetSafeStringPtr(pb.GetProperties().GetProductState()),
			HighlightIds:                helpers.GetSafeStringPtr(pb.GetProperties().GetHighlightIds()),
			WidgetGroupTrackingID:       helpers.GetSafeStringPtr(pb.GetProperties().GetWidgetGroupTrackingId()),
			EntrySourceGroupTrackingID:  helpers.GetSafeStringPtr(pb.GetProperties().GetEntrySourceGroupTrackingId()),
			VideoShown:                  helpers.GetSafeBoolPtr(pb.GetProperties().GetVideoShown()),
			ETAIdentifier:               helpers.GetSafeStringPtr(pb.GetProperties().GetEtaIdentifier()),
			WishlistAdded:               helpers.GetSafeBoolPtr(pb.GetProperties().GetWishlistAdded()),
			IsTopRightIconSelected:      helpers.GetSafeBoolPtr(pb.GetProperties().GetIsTopRightIconSelected()),
			IconType:                    helpers.GetSafeStringPtr(pb.GetProperties().GetIconType()),
		},
		URL:            envelope.Url,
		EventID:        envelope.EventId,
		SequenceID:     envelope.SequenceId,
		SequenceOffset: int64(envelope.SequenceOffset),
	}

	productImageShownEventString, err := json.MarshalToString(data)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(productImageShownEventString),
		Key:     key,
		Table:   envelope.GetTable(),
	}}, err
}

var NuggetCustomHandlingV2Map = CustomHandlingMap{}

var CustomHandlingJumboV2Map = map[string]CustomHandlingMap{
	constants.BLINKIT_TENANT:  BlinkitCustomHandlingV2Map,
	constants.DISTRICT_TENANT: DistrictCustomHandlingV2Map,
	constants.ZOMATO_TENANT:   ZomatoCustomHandlingV2Map,
	constants.NUGGET_TENANT:   NuggetCustomHandlingV2Map,
}

func getKarmaTrackingString(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &karmatracking.KarmaTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to karma event. Error: " + err.Error())
	}

	// to allow only event name ZOMATO_COD
	if pb.GetEventName() != karmatracking.EventName_ZOMATO_COD {
		return nil, nil
	}

	karmaEvents := models.KarmaEvents{
		UserID:                   envelope.GetHeader().GetUserId().GetValue(),
		SessionID:                envelope.GetHeader().GetSessionId().GetValue(),
		NmNationalProjectedScore: pb.GetModelRequest().GetZomatoCod().GetNmNationalProjectedScore().GetValue(),
		PBin:                     pb.GetModelRequest().GetZomatoCod().GetPBin().GetValue(),
		RestrictedTimeInterval:   pb.GetModelRequest().GetZomatoCod().GetRestrictedTimeInterval().GetValue(),
		CurrentTimeHrMin:         pb.GetModelRequest().GetZomatoCod().GetCurrentTimeHrMin().GetValue(),
		ExperimentBucket:         pb.GetModelRequest().GetZomatoCod().GetExperimentBucket().GetValue(),
		NewUserFlag:              pb.GetModelRequest().GetZomatoCod().GetNewUserFlag().GetValue(),
		CityID:                   envelope.GetHeader().GetLocation().GetValue(),
		DszID:                    pb.GetModelRequest().GetZomatoCod().GetDszId().GetValue(),
		ResID:                    pb.GetModelRequest().GetZomatoCod().GetResId().GetValue(),
		OfflineCodLimit:          pb.GetModelRequest().GetZomatoCod().GetOfflineCodLimit().GetValue(),
		RealtimeCodLimit:         pb.GetModelRequest().GetZomatoCod().GetRealtimeCodLimit().GetValue(),
		OfflineDszCodLimit:       pb.GetModelRequest().GetZomatoCod().GetOfflineDszCodLimit().GetValue(),
		OfflineResCodLimit:       pb.GetModelRequest().GetZomatoCod().GetOfflineResCodLimit().GetValue(),
		ModelCodLimit:            pb.GetModelRequest().GetZomatoCod().GetModelCodLimit().GetValue(),
		HighRiskFlag:             pb.GetModelRequest().GetZomatoCod().GetHighRiskFlag().GetValue(),
		IsEternalCustomer:        pb.GetModelRequest().GetZomatoCod().GetIsEternalCustomer().GetValue(),
		RealtimeRuleCodLimit:     pb.GetModelRequest().GetZomatoCod().GetRealtimeRuleCodLimit().GetValue(),
		RealtimeRuleName:         pb.GetModelRequest().GetZomatoCod().GetRealtimeRuleName().GetValue(),
		ActionReason:             pb.GetModelRequest().GetZomatoCod().GetActionReason().GetValue(),
		CodLimit:                 pb.GetModelResponse().GetZomatoCod().GetCodLimit().GetValue(),
		DefaultCodLimitFlag:      pb.GetModelResponse().GetZomatoCod().GetDefaultCodLimitFlag().GetValue(),
		PreviousKarmaScore:       pb.GetPreviousKarmaScore().String(),
		PreviousKarmaReason:      pb.GetPreviousKarmaReason().GetValue(),
		NmKarmaScore:             pb.GetNmKarmaScore().String(),
		NmKarmaReason:            pb.GetNmKarmaReason().GetValue(),
		NmKarmaPolicyLabel:       pb.GetNmKarmaPolicyLabel().String(),
		PreviousNmKarmaScore:     pb.GetPreviousNmKarmaScore().String(),
		PreviousNmKarmaReason:    pb.GetPreviousNmKarmaReason().GetValue(),
		CartTime:                 envelope.GetHeader().GetTimestamp().GetSeconds(),
	}
	karmaEventsString, err := json.MarshalToString(karmaEvents)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(karmaEventsString),
		Key:     key,
		Table:   envelope.GetTable(),
	}}, err
}

func getMerchantRelayEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &merchantrelayevents.MerchantRelayEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to karma event. Error: " + err.Error())
	}

	// Only allow prod events
	if pb.GetEnvironment() != merchantrelayevents.Environment_ENVIRONMENT_PRODUCTION {
		return nil, nil
	}

	// to get payload of everyday relay events
	if pb.GetEventSource() == merchantrelayevents.EventSource_EVENT_SOURCE_MX_WEB_EVERYDAY_DASHBOARD {
		return getEveryDayRelayEvents(pb, envelope.GetHeader().GetTimestamp().Seconds, key)
	}

	return nil, nil
}

func getEveryDayRelayEvents(pb *merchantrelayevents.MerchantRelayEvents, timestamp int64, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	if pb.GetEventName() != merchantrelayevents.EventName_EVENT_NAME_ORDER_STATUS_CHANGE {
		return nil, nil
	}

	everydayRelayEvents := models.EveryDayOrderRelayEvents{
		ResID:       pb.GetMetaData().GetResId().GetValue(),
		OrderStatus: pb.GetMetaData().GetOrderStatus().GetValue(),
		ResStatus:   pb.GetMetaData().GetResStatus().GetValue(),
		RelayMode:   pb.GetRelayMode().String(),
		Timestamp:   timestamp,
	}

	everydayRelayEventsString, err := json.MarshalToString(everydayRelayEvents)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(everydayRelayEventsString),
		Key:     key,
		Table:   "everyday_relay_events",
	}}, err
}

func getLiveOrderServiceEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &liveorderserviceevents.LiveOrderServiceEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to live order service event. Error: " + err.Error())
	}

	// to get payload of live order service events
	if _, ok := validLiveOrderServiceEvents[pb.GetEventName().GetValue()]; ok {
		liveOrderServiceEvents := models.LiveOrderServiceEvents{
			OrderId:          pb.GetOrderId().GetValue(),
			EventName:        pb.GetEventName().GetValue(),
			CarouselBannerId: pb.GetEventData().GetCarouselBannerId(),
			VideoBannerId:    pb.GetEventData().GetVideoBannerId(),
			Timestamp:        envelope.GetHeader().GetTimestamp().Seconds,
			UserId:           envelope.GetHeader().GetUserId().GetValue(),
			BannerId:         pb.GetEventData().GetBannerId(),
		}

		liveOrderServiceEventsString, err := json.MarshalToString(liveOrderServiceEvents)

		return []*models.JumboV2TransformedEvent{{
			Payload: []byte(liveOrderServiceEventsString),
			Key:     key,
			Table:   "crystal_brands_ads_events",
		}}, err
	}

	return nil, nil
}

func getAppPerformanceMetrics(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &appperformancemetrics.AppPerformanceMetrics{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to app perfomance metric event. Error: " + err.Error())
	}

	appPerformanceMetricEvent := models.AppPerformanceMetricsEvents{
		EventName:                   pb.GetEventName().GetValue(),
		PageName:                    pb.GetPageName().GetValue(),
		CountryId:                   pb.GetCountryId(),
		AppType:                     pb.GetAppType().GetValue(),
		AppVersion:                  pb.GetAppVersion().GetValue(),
		OsVersion:                   pb.GetOsVersion().GetValue(),
		DeviceName:                  pb.GetDeviceName().GetValue(),
		NetworkType:                 pb.GetNetworkType().GetValue(),
		NetworkOperator:             pb.GetNetworkOperator().GetValue(),
		EntityId:                    pb.GetEntityId().GetValue(),
		EntityType:                  pb.GetEntityType().GetValue(),
		BusinessType:                pb.GetBusinessType().GetValue(),
		Duration:                    pb.GetDuration(),
		Component:                   pb.GetComponent().GetValue(),
		AppState:                    pb.GetAppState().GetValue(),
		FrameInfoSlowFrames:         pb.GetFrameInfo().GetSlowFrames(),
		FrameInfoFrameCounts:        pb.GetFrameInfo().GetFrameCounts(),
		MemoryInfoAppMemory:         pb.GetMemoryInfo().GetAppMemory(),
		MemoryInfoScreenMemory:      pb.GetMemoryInfo().GetScreenMemory(),
		MemoryInfoAvailableOSMemory: pb.GetMemoryInfo().GetAvailableOsMemory(),
		ScreenTime:                  pb.GetScreenTime(),
		IsDebug:                     pb.GetIsDebug(),
		CityID:                      int64(envelope.GetHeader().GetLocation().GetValue()),
		Timestamp:                   envelope.GetHeader().GetTimestamp().Seconds,
	}

	appPerformanceMetricEventString, err := json.MarshalToString(appPerformanceMetricEvent)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(appPerformanceMetricEventString),
		Key:     key,
		Table:   "app_performance_metrics",
	}}, err
}

func getServiceabilityLocalityStats(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &serviceabilitylocalitystats.ServiceabilityLocalityStats{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to LocalityStats event. Error: " + err.Error())
	}

	serviceabilitylocalitystatsEvent := models.ServiceabilityLocalityStats{
		LocalityID:                 pb.GetLocalityId(),
		LineOfBusiness:             pb.GetLineOfBusiness().String(),
		CarrierType:                pb.GetCarrierType().String(),
		OrderRiderRatio:            pb.GetOrderRiderRatio(),
		FreeDriverMatchedCount:     pb.GetFreeDriverCount().GetMatchedDriverCount(),
		FreeDriverUnmatchedCount:   pb.GetFreeDriverCount().GetUnmatchedDriverCount(),
		QueuedDriverMatchedCount:   pb.GetQueuedDriverCount().GetMatchedDriverCount(),
		QueuedDriverUnmatchedCount: pb.GetQueuedDriverCount().GetUnmatchedDriverCount(),
		Timestamp:                  envelope.GetHeader().GetTimestamp().Seconds,
	}

	serviceabilitylocalitystatsEventString, err := json.MarshalToString(serviceabilitylocalitystatsEvent)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(serviceabilitylocalitystatsEventString),
		Key:     key,
		Table:   "serviceability_locality_stats",
	}}, err
}

func getAdsCentralWebTracking(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &adscentralwebtracking.AdsCentralWebTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to AdsCentralWebTracking event. Error: " + err.Error())
	}

	adscentralwebtrackingEvent := models.AdsCentralWebTracking{
		Timestamp: envelope.GetHeader().GetTimestamp().Seconds,
		Url:       pb.GetWebTracking().GetUrlSegment().String(),
		EventType: pb.GetWebTracking().GetEventType().String(),
		EventName: pb.GetEventName().String(),
	}

	adscentralwebtrackingEventString, err := json.MarshalToString(adscentralwebtrackingEvent)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(adscentralwebtrackingEventString),
		Key:     key,
		Table:   "ads_central_web_tracking",
	}}, err
}

func getWebviewsTracking(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &webviewstracking.WebviewTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to WebviewTracking event. Error: " + err.Error())
	}

	webviewstrackingEvent := models.WebviewTracking{
		Timestamp:   envelope.GetHeader().GetTimestamp().Seconds,
		Url:         pb.GetWebTracking().GetUrlSegment().String(),
		EventType:   pb.GetWebTracking().GetEventType().String(),
		Component:   pb.GetWebTracking().GetComponent().String(),
		WebviewName: pb.GetWebviewName(),
	}

	webviewstrackingEventString, err := json.MarshalToString(webviewstrackingEvent)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(webviewstrackingEventString),
		Key:     key,
		Table:   "webviews_tracking",
	}}, err
}

func getAdsStudioTracking(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &adsstudiotracking.AdsStudioTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to AdsStudioTracking event. Error: " + err.Error())
	}

	adsstudiotrackingEvent := models.AdsStudioTracking{
		Timestamp: envelope.GetHeader().GetTimestamp().Seconds,
		Url:       pb.GetWebTracking().GetUrlSegment().String(),
		EventType: pb.GetWebTracking().GetEventType().String(),
		Component: pb.GetWebTracking().GetComponent().String(),
	}

	adsstudiotrackingEventString, err := json.MarshalToString(adsstudiotrackingEvent)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(adsstudiotrackingEventString),
		Key:     key,
		Table:   "ads_studio_tracking",
	}}, err
}

func getDdtSalienceTrackingEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &ddtsaliencetracking.DDTSalienceTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to DdtSalienceTracking event. Error: " + err.Error())
	}

	ingestion_timestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestion_timestamp == 0 {
		ingestion_timestamp = time.Now().Unix()
	}

	ddtSalienceTrackingEvent := models.DDTSalienceTrackingEvents{
		Source:              envelope.GetHeader().GetSource().String(),
		SessionID:           envelope.GetHeader().GetSessionId().GetValue(),
		UserID:              envelope.GetHeader().GetUserId().GetValue(),
		UserAgent:           envelope.GetHeader().GetUserAgent().GetValue(),
		Timestamp:           envelope.GetHeader().GetTimestamp().GetSeconds(),
		IngestionTime:       ingestion_timestamp,
		Location:            int32(envelope.GetHeader().GetLocation().GetValue()),
		SearchID:            pb.GetSearchId().GetValue(),
		TraceID:             pb.GetTraceId().GetValue(),
		ResID:               uint32(pb.GetResId().GetValue()),
		ArrivalAfterKPT:     float64(pb.GetArrivalAfterKpt().GetValue()),
		Buffer:              float64(pb.GetBuffer().GetValue()),
		BaseDDT:             float64(pb.GetBaseDdt().GetValue()),
		Rank:                int32(pb.GetRank().GetValue()),
		SAStatusCode:        int32(pb.GetSaStatusCode().GetValue()),
		EFOSAStatusCode:     int32(pb.GetEfoSaStatusCode().GetValue()),
		ApproxSAETA:         float64(pb.GetApproxSaEta().GetValue()),
		RestaurantLongitude: pb.GetRestaurantDetails().GetCoordinate().GetLongitude().GetValue(),
		RestaurantLatitude:  pb.GetRestaurantDetails().GetCoordinate().GetLatitude().GetValue(),
		RestaurantCellID:    pb.GetRestaurantDetails().GetS2CellId(),
		UserLongitude:       pb.GetUserDetails().GetCoordinate().GetLongitude().GetValue(),
		UserLatitude:        pb.GetUserDetails().GetCoordinate().GetLatitude().GetValue(),
		UserCellID:          pb.GetUserDetails().GetS2CellId(),
	}

	ddtSalienceTrackingEventString, err := json.MarshalToString(ddtSalienceTrackingEvent)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(ddtSalienceTrackingEventString),
		Key:     key,
		Table:   "ddt_salience_tracking",
	}}, err
}

func getEventsResStatusUpdateEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &mxoutletsnapshotevents.MxOutletSnapshotEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to MxOutletChangelogEvents event. Error: " + err.Error())
	}

	eventsResStatusUpdateEvent := models.EventsResStatusUpdateEvents{
		ResId:                      pb.GetResId(),
		EventType:                  pb.GetEventType().String(),
		AdminActiveStatus:          pb.GetUptimeFlagUpdated().GetAdminActiveStatus().GetAdminActiveStatus(),
		DeliveryStatus:             pb.GetUptimeFlagUpdated().GetDeliveryStatus().GetDeliveryStatus(),
		IsDeliveryStatusUpdated:    pb.GetUptimeFlagUpdated().GetDeliveryStatus().GetIsUpdated(),
		IsAdminActiveStatusUpdated: pb.GetUptimeFlagUpdated().GetAdminActiveStatus().GetIsUpdated(),
	}

	eventsResStatusUpdateEventString, err := json.MarshalToString(eventsResStatusUpdateEvent)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventsResStatusUpdateEventString),
		Key:     key,
		Table:   "mx_outlet_snapshot_events",
	}}, err
}
func getDistrictEventsTicketEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &eventsticket.EventsTicket{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to EventsBusinessTracking event. Error: " + err.Error())
	}

	ingestionTimestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestionTimestamp == 0 {
		ingestionTimestamp = time.Now().Unix()
	}

	// Construct the flattened structure
	eventsTicket := models.EventsTicket{
		Source:        envelope.GetHeader().GetSource().String(),
		DeviceID:      envelope.GetHeader().GetDeviceId().GetValue(),
		SessionID:     envelope.GetHeader().GetSessionId().GetValue(),
		UserID:        envelope.GetHeader().GetUserId().GetValue(),
		Timestamp:     envelope.GetHeader().GetTimestamp().GetSeconds(),
		IngestionTime: ingestionTimestamp,
		Location:      int32(envelope.GetHeader().GetLocation().GetValue()),

		DistrictEventID: pb.GetDistrictEventId().GetValue(),
		ShowID:          pb.GetShowId().GetValue(),
		VenueID:         pb.GetVenueId().GetValue(),
		GroupID:         pb.GetGroupId().GetValue(),
		PhaseGroupID:    pb.GetPhaseGroupId().GetValue(),
		ItemID:          pb.GetItemId().GetValue(),
		TicketType:      pb.GetTicketType().String(),
		TicketID:        pb.GetTicketId().GetValue(),
		OrderID:         pb.GetOrderId().GetValue(),
		Category:        pb.GetCategory().GetValue(),
		TicketBasePrice: pb.GetTicketBasePriceV2().GetValue(),
		Discount:        pb.GetDiscount().GetValue(),
		Action:          pb.GetAction().String(),
		UtmSource:       pb.GetUtmParams().GetUtmSource().GetValue(),
		UtmMedium:       pb.GetUtmParams().GetUtmMedium().GetValue(),
		UtmCampaign:     pb.GetUtmParams().GetUtmCampaign().GetValue(),
	}

	eventsTicketString, err := json.MarshalToString(eventsTicket)
	tableKey := []byte(pb.GetTicketId().GetValue())
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventsTicketString),
		Key:     tableKey,
		Table:   "events_ticket",
		Topic:   "insights.district.events_ticket",
		Broker:  constants.DEFAULT_BROKER,
	}}, err
}

func getEventsBusinessTrackingEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &eventsbussinesstracking.EventsBussinessTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to EventsBusinessTracking event. Error: " + err.Error())
	}

	ingestion_timestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestion_timestamp == 0 {
		ingestion_timestamp = time.Now().Unix()
	}

	eventsbusinesstrackingEvent := models.EventsBusinessTrackingEvents{
		Source:                 envelope.GetHeader().GetSource().String(),
		DeviceID:               envelope.GetHeader().GetDeviceId().GetValue(),
		SessionID:              envelope.GetHeader().GetSessionId().GetValue(),
		UserID:                 envelope.GetHeader().GetUserId().GetValue(),
		UserAgent:              envelope.GetHeader().GetUserAgent().GetValue(),
		Timestamp:              envelope.GetHeader().GetTimestamp().GetSeconds(),
		IngestionTime:          ingestion_timestamp,
		Location:               int32(envelope.GetHeader().GetLocation().GetValue()),
		EventName:              pb.GetEventName().GetValue(),
		EventType:              pb.GetEventType().String(),
		MainEventID:            pb.GetMainEventId().GetValue(),
		MainEventCategory:      pb.GetMainEventCategory().GetValue(),
		MainEventDate:          pb.GetMainEventDate().GetDate().GetValue(),
		MainEventDuration:      pb.GetMainEventDate().GetDuration().GetValue(),
		Page:                   pb.GetPage().GetValue(),
		Section:                pb.GetSection().GetValue(),
		URL:                    envelope.GetUrl(),
		UtmSource:              pb.GetUtmParams().GetUtmSource().GetValue(),
		UtmMedium:              pb.GetUtmParams().GetUtmMedium().GetValue(),
		UtmCampaign:            pb.GetUtmParams().GetUtmCampaign().GetValue(),
		TimerCountdown:         pb.GetTimerCountdown().GetValue(),
		PrevDateTimestamp:      pb.GetPrevDateTimestamp().GetValue(),
		NewDateTimestamp:       pb.GetNewDateTimestamp().GetValue(),
		PageType:               pb.GetPageType().GetValue(),
		ErrorMessage:           pb.GetErrorMessage().GetValue(),
		CTA:                    pb.GetCta().GetValue(),
		DistrictUserId:         pb.GetDistrictUserId().GetValue(),
		Message:                pb.GetMessage().GetValue(),
		PreviousSection:        pb.GetPreviousSection().GetValue(),
		QueueID:                pb.GetQueueInfo().GetQueueId().GetValue(),
		QueuePosition:          pb.GetQueueInfo().GetQueuePosition().GetValue(),
		QueueEstimatedWaitTime: pb.GetQueueInfo().GetEstimatedWaitTime().GetValue(),
		QueueStatus:            pb.GetQueueInfo().GetQueueStatus().GetValue(),
		QueuePassType:          pb.GetQueueInfo().GetQueuePassType().GetValue(),
	}

	eventsbusinesstrackingEventString, err := json.MarshalToString(eventsbusinesstrackingEvent)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventsbusinesstrackingEventString),
		Key:     key,
		Table:   "events_business_tracking",
	}}, err
}

func getDistrictHomepageTrackingEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &homepagetracking.HomepageTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to EventsBusinessTracking event. Error: " + err.Error())
	}

	ingestion_timestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestion_timestamp == 0 {
		ingestion_timestamp = time.Now().Unix()
	}

	homepageTrackingEvents := models.DistrictHomepageTrackingEvents{
		Source:        envelope.GetHeader().GetSource().String(),
		DeviceID:      envelope.GetHeader().GetDeviceId().GetValue(),
		SessionID:     envelope.GetHeader().GetSessionId().GetValue(),
		UserID:        envelope.GetHeader().GetUserId().GetValue(),
		Timestamp:     envelope.GetHeader().GetTimestamp().GetSeconds(),
		IngestionTime: ingestion_timestamp,
		Location:      int32(envelope.GetHeader().GetLocation().GetValue()),
		PageType:      pb.GetPageType().GetValue(),
		Section:       pb.GetSection().GetValue(),
		EntityId:      pb.GetEntityId().GetValue(),
		EntityType:    pb.GetEntityType().GetValue(),
		Action:        pb.GetAction().String(),
	}

	homepageTrackingEventString, err := json.MarshalToString(homepageTrackingEvents)
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(homepageTrackingEventString),
		Key:     key,
		Table:   "homepage_tracking",
	}}, err
}

func getHyperpureOrderEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &hyperpureorderevents.HyperpureOrderEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to HyperpureOrderEvents event. Error: " + err.Error())
	}

	if pb.GetVersion() != hyperpureorderevents.Version_V2 {
		return nil, nil
	}

	warehouseCode := pb.GetWarehouseCode().GetValue()
	if !(strings.HasPrefix(warehouseCode, "WH") ||
		strings.Contains(warehouseCode, "SW") ||
		strings.Contains(warehouseCode, "DS")) {
		return nil, nil
	}

	ingestion_timestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestion_timestamp == 0 {
		ingestion_timestamp = time.Now().Unix()
	}

	event := models.HyperpureOrderEvents{
		Timestamp:        envelope.GetHeader().GetTimestamp().GetSeconds(),
		IngestionTime:    ingestion_timestamp,
		OrderPlacedTime:  pb.GetOrderPlacedTime().GetSeconds(),
		BuyerOutletID:    pb.GetBuyerOutletId(),
		AccountID:        pb.GetAccountId().GetValue(),
		OrderNumber:      pb.GetOrderNumber(),
		OrderID:          pb.GetOrderId().GetValue(),
		TotalOrderPrice:  float64(pb.GetTotalOrderPrice().GetValue()),
		TotalOrderWeight: float64(pb.GetTotalOrderWeight().GetValue()),
		OrderStatus:      pb.GetOrderStatus().String(),
		WarehouseCode:    warehouseCode,
		Version:          pb.GetVersion().String(),
	}

	eventString, err := json.MarshalToString(event)
	if err != nil {
		logger.WithError(err).Error("unable to cast prepared payload map to json")
		return nil, err
	}
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventString),
		Table:   "hyperpure_order_events",
		Key:     []byte(fmt.Sprint(event.OrderID)),
	}}, err
}

func getSearchServiceabilityEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &serviceability.SearchServiceability{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to SearchServiceability event. Error: " + err.Error())
	}

	ingestion_timestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestion_timestamp == 0 {
		ingestion_timestamp = time.Now().Unix()
	}

	var statusCodeCounts []*models.SearchServiceabilityStatusCodeCounts
	for _, statusCodeCount := range pb.GetStatusCodeCounts() {
		statusCodeCounts = append(statusCodeCounts, &models.SearchServiceabilityStatusCodeCounts{
			ResCount:   statusCodeCount.GetResCount().GetValue(),
			StatusCode: statusCodeCount.GetStatusCode().GetValue(),
		})
	}

	event := &models.SearchServiceabilityCellSufficiency{
		CityID:           int32(envelope.GetHeader().GetLocation().GetValue()),
		Timestamp:        envelope.GetHeader().GetTimestamp().Seconds,
		IngestionTime:    ingestion_timestamp,
		DeliveryCellId:   pb.GetDeliveryCellId().GetValue(),
		CheckPoint:       pb.GetCheckpoint().GetValue(),
		StatusCodeCounts: statusCodeCounts,
		RequestId:        pb.GetRequestId().GetValue(),
		SourceType:       pb.GetSourceType().String(),
	}

	eventString, err := json.MarshalToString(event)
	if err != nil {
		logger.WithError(err).Error("unable to cast prepared payload map to json")
		return nil, err
	}
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventString),
		Table:   "search_serviceability_cell_sufficiency",
		Key:     []byte(fmt.Sprint(event.CityID)),
	}}, err
}

func getPageViewEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &pageview.Pageview{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to HyperpureOrderEvents event. Error: " + err.Error())
	}

	if pb.GetPagetype().GetValue() != "Restaurant" {
		return nil, nil
	}

	var eventName string
	switch {
	case strings.HasSuffix(envelope.GetUrl(), "order/verify"):
		eventName = "cartOpen"
	case strings.HasSuffix(envelope.GetUrl(), "order"):
		eventName = "menuOpen"
	case (strings.Contains(envelope.GetUrl(), "order") &&
		!strings.Contains(envelope.GetUrl(), "verify") &&
		!strings.HasSuffix(envelope.GetUrl(), "order") &&
		!strings.Contains(envelope.GetUrl(), "contextual_menu_params") &&
		pb.GetReferrer().GetValue() == "https://www.zomato.com/zpaykit/init"):
		eventName = "crystalOpen"
	default:
		return nil, nil
	}

	ingestion_timestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestion_timestamp == 0 {
		ingestion_timestamp = time.Now().Unix()
	}

	event := models.WebOrderEvents{
		Timestamp:     envelope.GetHeader().GetTimestamp().GetSeconds(),
		IngestionTime: ingestion_timestamp,
		EventName:     eventName,
		CityID:        int32(envelope.GetHeader().GetLocation().GetValue()),
		Source:        envelope.GetHeader().GetSource().String(),
	}

	eventString, err := json.MarshalToString(event)
	if err != nil {
		logger.WithError(err).Error("unable to cast prepared payload map to json")
		return nil, err
	}
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventString),
		Table:   "web_order_events",
		Key:     []byte(fmt.Sprint(event.CityID)),
	}}, err
}

func getEtaTracking(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &etatracking.EtaTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshalany to EtaTracking event. Error: " + err.Error())
	}

	if !transformers.Contains([]string{"ddtv3", "pingwise", "edt", "ix_ddt", "ix_edt"}, pb.GetCategory().GetValue()) {
		return nil, nil
	}

	metadata := pb.GetMetadata()
	event := models.EtaTrackingV2{
		AcceptanceTime:        pb.GetAcceptanceTime().GetValue(),
		Buffer:                pb.GetBuffer().GetValue(),
		Category:              pb.GetCategory().GetValue(),
		DeviceID:              envelope.GetHeader().GetDeviceId().GetValue(),
		DropDistance:          pb.GetDropDistance().GetValue(),
		DropEta:               float64(pb.GetDropEta().GetValue()),
		Kpt:                   float64(pb.GetKpt().GetValue()),
		KptHandoverTime:       float64(pb.GetKptHandoverTime().GetValue()),
		Location:              fmt.Sprint(envelope.GetHeader().GetLocation().GetValue()),
		MetadataCallerService: metadata["caller_service"],
		MetadataEstablishment: metadata["establishment"],
		MetadataIsLogs:        metadata["is_logs"],
		MetadataKptBuffer:     metadata["kpt_buffer"],
		MetadataRdtBuffer:     metadata["rdt_buffer"],
		OrderID:               pb.GetOrderId().GetValue(),
		OrderStatus:           pb.GetOrderStatus().GetValue(),
		PickupDistance:        pb.GetPickupDistance().GetValue(),
		PickupEta:             pb.GetPickupEta().GetValue(),
		PingTimestamp:         pb.GetPingTimestamp().GetValue(),
		Polyline:              pb.GetPolyline().GetValue(),
		Rat:                   float64(pb.GetRat().GetValue()),
		RawEta:                float64(pb.GetRawEta().GetValue()),
		ResID:                 int(pb.GetResId().GetValue()),
		ResLatitude:           pb.GetResLatitude().GetValue(),
		ResLongitude:          pb.GetResLongitude().GetValue(),
		RiderLatitude:         pb.GetRiderLatitude().GetValue(),
		RiderLongitude:        pb.GetRiderLongitude().GetValue(),
		SessionID:             envelope.GetHeader().GetSessionId().GetValue(),
		SmoothenEta:           float64(pb.GetSmoothenEta().GetValue()),
		Source:                envelope.GetHeader().GetSource().String(),
		TimeToReach:           pb.GetTimeToReach().GetValue(),
		UserAgent:             envelope.GetHeader().GetUserAgent().GetValue(),
		UserID:                envelope.GetHeader().GetUserId().GetValue(),
		UserLatitude:          pb.GetUserLatitude().GetValue(),
		UserLongitude:         pb.GetUserLongitude().GetValue(),
		Timestamp:             envelope.GetHeader().GetTimestamp().AsTime().Format(time.RFC3339),
	}

	eventString, err := json.MarshalToString(event)
	if err != nil {
		logger.WithError(err).Error("unable to cast prepared payload map to json")
		return nil, err
	}

	// Step 6: Prepare the transformed event
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventString),
		Key:     key,
		Table:   "eta_tracking",
	}}, err
}

func getMoviesEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &moviesevents.MoviesEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to MoviesEvents event. Error: " + err.Error())
	}

	app_tracking := pb.GetAppTracking()
	page_navigation := pb.GetPageNaviagtion()
	page_paint := pb.GetPagePaint()
	payment_details := pb.GetPaymentDetails()
	seat_details := pb.GetSeatDetails()
	ingestion_timestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestion_timestamp == 0 {
		ingestion_timestamp = time.Now().Unix()
	}
	var selectedSeats []string
	for _, seat := range pb.GetSelectedSeats() {
		selectedSeats = append(selectedSeats, seat.GetValue())
	}
	event := models.MoviesEvents{
		Source:                          envelope.GetHeader().GetSource().String(),
		DeviceID:                        envelope.GetHeader().GetDeviceId().GetValue(),
		SessionID:                       envelope.GetHeader().GetSessionId().GetValue(),
		UserID:                          envelope.GetHeader().GetUserId().GetValue(),
		UserAgent:                       envelope.GetHeader().GetUserAgent().GetValue(),
		Timestamp:                       envelope.GetHeader().GetTimestamp().GetSeconds(),
		IngestionTime:                   ingestion_timestamp,
		AppTrackingAppType:              app_tracking.GetAppVersion().GetValue(),
		AppTrackingAppVersion:           app_tracking.GetAppVersion().GetValue(),
		AppTrackingOsVersion:            app_tracking.GetOsVersion().GetValue(),
		AppTrackingDeviceName:           app_tracking.GetDeviceName().GetValue(),
		AppTrackingEventType:            app_tracking.GetEventType().String(),
		PageNavigationClickTime:         page_navigation.GetClickTime().GetValue(),
		PageNavigationFirstRender:       page_navigation.GetFirstRender().GetValue(),
		PageNavigationFullRender:        page_navigation.GetFirstRender().GetValue(),
		PagePaintApi:                    page_paint.GetApi().GetValue(),
		PagePaintComponent:              page_paint.GetComponent().GetValue(),
		PagePaintTotal:                  page_paint.GetTotal().GetValue(),
		PaymentDetailsMethodType:        payment_details.GetMethodType().GetValue(),
		PaymentDetailsDisplayName:       payment_details.GetDisplayName().GetValue(),
		CinemaDistance:                  pb.GetCinemaDistance().GetValue(),
		ErrorCode:                       pb.GetErrorCode().GetValue(),
		ErrorMessage:                    pb.GetErrorMessage().GetValue(),
		EventName:                       pb.EventName.GetValue(),
		EventType:                       pb.GetEventType().String(),
		Location:                        int32(envelope.GetHeader().GetLocation().GetValue()),
		MovieId:                         pb.GetMovieId().GetValue(),
		OrderId:                         pb.GetOrderId().GetValue(),
		Page:                            pb.GetPage().GetValue(),
		PreviousPage:                    pb.GetPreviousPage().GetValue(),
		PromoApplied:                    pb.GetPromoApplied().GetValue(),
		PromoCountAvailable:             pb.GetPromoCountAvailable().GetValue(),
		PromoUpfront:                    pb.GetPromoUpfront().GetValue(),
		TheatreId:                       pb.GetTheatreId().GetValue(),
		TicketQuantity:                  pb.GetTicketQuantity().GetValue(),
		ToastMessageText:                pb.GetToastMessageText().GetValue(),
		SessionAvailability:             pb.GetSessionAvailability().Enum().String(),
		MovieSessionId:                  pb.GetMovieSessionId().GetValue(),
		Language:                        pb.GetLanguage().GetValue(),
		Format:                          pb.GetFormat().GetValue(),
		ScreenName:                      pb.GetScreenName().GetValue(),
		MerchantCancellationEligibility: pb.GetMerchantCancellationEligibility().GetValue(),
		OfferHandlingFeeAdded:           pb.GetOfferHandlingFeeAdded().GetValue(),
		BillSummaryTaxesFees:            pb.GetBillSummaryTaxesFees().GetValue(),
		BillSummaryTicket:               pb.GetBillSummaryTicket().GetValue(),
		BillSummaryFood:                 pb.GetBillSummaryFood().GetValue(),
		BillSummary3d:                   pb.GetBillSummary_3D().GetValue(),
		BillingState:                    pb.GetBillingState().GetValue(),
		BilllingDiscount:                pb.GetBilllingDiscount().GetValue(),
		IsMerchantLoyalityApplied:       pb.GetIsMerchantLoyalityApplied().GetValue(),
		MerchantLoyaltyBenefits:         pb.GetMerchantLoyaltyBenefits().GetValue(),
		NoOfShows:                       pb.GetNoOfShows().GetValue(),
		NoOfCinemas:                     pb.GetNoOfCinemas().GetValue(),
		NoOfMovies:                      pb.GetNoOfMovies().GetValue(),
		TheatreLiked:                    pb.GetTheatreLiked().GetValue(),
		PrevBooked:                      pb.GetPrevBooked().GetValue(),
		HorizontalRank:                  pb.GetHorizontalRank().GetValue(),
		Platform:                        pb.GetPlatform().Enum().String(),
		SelectedSeats:                   selectedSeats,
		SeatDetailsPrice:                seat_details.GetPrice().GetValue(),
		SeatDetailsClass:                seat_details.GetClass().GetValue(),
		SeatDetailsAvailability:         seat_details.GetAvailability().GetValue(),
	}

	eventString, err := json.MarshalToString(event)
	if err != nil {
		logger.WithError(err).Error("unable to cast prepared payload map to json")
		return nil, err
	}

	// Step 6: Prepare the transformed event
	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventString),
		Key:     key,
		Table:   "movies_events",
	}}, err
}

func getJAdTrackingEvent(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &jadtracking.JAdTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to JadTracking event. Error: " + err.Error())
	}

	action := pb.GetAction().GetValue()
	if pb.GetCategoryId().GetValue() == "1" && helpers.IsInStringSlice(action, []string{"impression", "click"}) {
		event, err := GetJumboV2FlattenedJSONMap(envelope, constants.DISTRICT_TENANT, key)
		if err != nil {
			return event, err
		}
		event[0].Topic = fmt.Sprintf(`jumbo_transformed.district.adtech-%s`, action)
		event[0].Broker = constants.DISTRICT_TENANT
	}
	return nil, nil
}

func getDistrictAdsEvent(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &jadtracking.JAdTracking{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to JadTracking event. Error: " + err.Error())
	}

	action := pb.GetAction().GetValue()
	if !helpers.IsInStringSlice(action, []string{"impression", "click"}) {
		logger.WithFields(map[string]interface{}{
			"action": action,
			"table":  envelope.Table,
		}).Debug("Event is not required")
		return nil, nil
	}

	event, err := GetJumboV2FlattenedJSONMap(envelope, constants.DISTRICT_TENANT, key)
	if err != nil {
		return nil, err
	}
	event[0].Table = fmt.Sprintf(`adtech-%s`, action)
	return event, err
}

func getDriverObAppsflyerEvent(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &appsflyerevents.AppsflyerEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal to AppsflyerEvents with Error: " + err.Error())
	}

	if !helpers.IsInStringSlice(pb.GetEventName().GetValue(),
		[]string{"ob_app_login", "ob_payment_success", "ob_complete"}) {
		logger.Infof("[getDriverObAppsflyerEvent] Event %s is not required for transformation ", pb.GetEventName().GetValue())
		return nil, nil
	}

	if pb.GetEventValue()["user_number"] == "" {
		logger.Info("[getDriverObAppsflyerEvent] User number is empty in the event")
		return nil, nil
	}

	business, ok := constants.RiderAppMapping[pb.GetAppId().GetValue()]
	if !ok {
		logger.Infof("[getDriverObAppsflyerEvent] Business not found for the given app id %s", pb.GetAppId().GetValue())
		return nil, nil
	}

	protoMessage := &lms.LeadEvent{
		ClientId:    constants.DriverLeadClientID,
		PhoneNumber: pb.GetEventValue()["user_number"],
		EventType:   lms.EventType_EVENT_TYPE_UPSERT,
		SourceId:    pb.GetCampaign().GetValue(),
		LeadType:    lms.LeadType_LEAD_TYPE_DRIVER,
		LeadTypeInfo: &lms.LeadEvent_DriverLeadInfo{
			DriverLeadInfo: &lms.DriverLeadInfo{
				Source:      lms.DriverLeadSource_DRIVER_LEAD_SOURCE_DIGITAL,
				SubSource:   pb.GetMediaSource().GetValue(),
				UtmCampaign: pb.GetCampaign().GetValue(),
				Business:    business,
				AppsflyerData: &lms.AppsflyerData{
					AppInstallTimestamp:  pb.InstallTime.GetValue(),
					AttributionTimestamp: pb.AttributedTouchTime.GetValue(),
					MediaSource:          pb.MediaSource.GetValue(),
					EventName:            pb.GetEventName().GetValue(),
					Campaign:             pb.Campaign.GetValue(),
					AfChannel:            pb.AfChannel.GetValue(),
					AfSiteId:             pb.AfSiteid.GetValue(),
				},
			},
		},
		LocationDetails: &lms.LocationDetails{
			CityName: pb.GetCity().GetValue(),
		},
	}

	anyMessage, err := anypb.New(protoMessage)
	if err != nil {
		logger.Error("[getDriverObAppsflyerEvent] Error in marshalling the message into Any", zap.Error(err))
		return nil, err
	}

	// new UUID for each event
	nUuid := uuid.New().String()
	// Time is in UTC
	reqTime := time.Now()

	zEvent := &event.Event{
		Header: &event.Header{
			TraceId: nUuid,
			Uuid:    nUuid,
			Timestamp: &timestamppb.Timestamp{
				Seconds: reqTime.Unix(),
				Nanos:   int32(reqTime.Nanosecond()),
			},
		},
		Payload: anyMessage,
	}

	payload, err := proto.Marshal(zEvent)
	if err != nil {
		logger.Error("[getDriverObAppsflyerEvent] Error in marshalling the message into AppsflyerEvents", zap.Error(err))
		return nil, err
	}
	partitionKey := fmt.Sprintf("%s#%s", constants.DriverLeadClientID, pb.GetEventValue()["user_number"])

	return []*models.JumboV2TransformedEvent{{
		Payload: payload,
		Table:   "appsflyer_events",
		Key:     []byte(partitionKey),
		Topic:   "logistics.lead-management-service.lead-event-tier2",
		Broker:  constants.OFFLINE_MSK,
	}}, err
}

func getDriverObBlinkitDeliveryPartnerAppsflyerEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &blinkitdeliverypartnerappsflyerevents.BlinkitDeliveryPartnerAppsflyerEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal to BlinkitDeliveryPartnerAppsflyerEvents with Error: " + err.Error())
	}

	if !helpers.IsInStringSlice(pb.GetEventName().GetValue(),
		[]string{"ob_app_login", "ob_payment_success", "ob_complete"}) {
		logger.Infof("[getDriverObBlinkitDeliveryPartnerAppsflyerEvents] Event %s is not required for transformation ", pb.GetEventName().GetValue())
		return nil, nil
	}

	if pb.GetEventValue()["user_number"] == "" {
		logger.Info("[getDriverObBlinkitDeliveryPartnerAppsflyerEvents] User number is empty in the event")
		return nil, nil
	}

	business, ok := constants.RiderAppMapping[pb.GetAppId().GetValue()]
	if !ok {
		logger.Infof("[getDriverObBlinkitDeliveryPartnerAppsflyerEvents] Business not found for the given app id %s", pb.GetAppId().GetValue())
		return nil, nil
	}

	protoMessage := &lms.LeadEvent{
		ClientId:    constants.DriverLeadClientID,
		PhoneNumber: pb.GetEventValue()["user_number"],
		EventType:   lms.EventType_EVENT_TYPE_UPSERT,
		SourceId:    pb.GetCampaign().GetValue(),
		LeadType:    lms.LeadType_LEAD_TYPE_DRIVER,
		LeadTypeInfo: &lms.LeadEvent_DriverLeadInfo{
			DriverLeadInfo: &lms.DriverLeadInfo{
				Source:      lms.DriverLeadSource_DRIVER_LEAD_SOURCE_DIGITAL,
				SubSource:   pb.GetMediaSource().GetValue(),
				UtmCampaign: pb.GetCampaign().GetValue(),
				Business:    business,
				AppsflyerData: &lms.AppsflyerData{
					AppInstallTimestamp:  pb.InstallTime.GetValue(),
					AttributionTimestamp: pb.AttributedTouchTime.GetValue(),
					MediaSource:          pb.MediaSource.GetValue(),
					EventName:            pb.GetEventName().GetValue(),
					Campaign:             pb.Campaign.GetValue(),
					AfChannel:            pb.AfChannel.GetValue(),
					AfSiteId:             pb.AfSiteid.GetValue(),
				},
			},
		},
		LocationDetails: &lms.LocationDetails{
			CityName: pb.GetCity().GetValue(),
		},
	}

	anyMessage, err := anypb.New(protoMessage)
	if err != nil {
		logger.Error("[getDriverObBlinkitDeliveryPartnerAppsflyerEvents] Error in marshalling the message into Any", zap.Error(err))
		return nil, err
	}

	// new UUID for each event
	nUuid := uuid.New().String()
	// Time is in UTC
	reqTime := time.Now()

	zEvent := &event.Event{
		Header: &event.Header{
			TraceId: nUuid,
			Uuid:    nUuid,
			Timestamp: &timestamppb.Timestamp{
				Seconds: reqTime.Unix(),
				Nanos:   int32(reqTime.Nanosecond()),
			},
		},
		Payload: anyMessage,
	}

	payload, err := proto.Marshal(zEvent)
	if err != nil {
		logger.Error("[getDriverObBlinkitDeliveryPartnerAppsflyerEvents] Error in marshalling the message into BlinkitDeliveryPartnerAppsflyerEvents", zap.Error(err))
		return nil, err
	}
	partitionKey := fmt.Sprintf("%s#%s", constants.DriverLeadClientID, pb.GetEventValue()["user_number"])

	return []*models.JumboV2TransformedEvent{{
		Payload: payload,
		Key:     []byte(partitionKey),
		Topic:   "logistics.lead-management-service.lead-event-tier2",
		Broker:  constants.OFFLINE_MSK,
	}}, err
}

func getO2CartTrackingEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	if envelope == nil || envelope.Payload == nil {
		return []*models.JumboV2TransformedEvent{}, nil
	}

	pb := &o2carttracking.O2CartTracking{}
	if err := anypb.UnmarshalTo(envelope.Payload, pb, proto.UnmarshalOptions{}); err != nil {
		logger.WithError(err).Error("unable to unmarshal any payload to the specific class")
		return nil, err
	}

	jeventPayloads, err := getO2CartTrackingPayload(pb)
	if err != nil {
		logger.Error("[getO2CartTrackingEvents] Error in getting the o2carttracking payload ", zap.Error(err))
		return nil, err
	}

	if len(jeventPayloads) == 0 {
		return nil, nil
	}

	transformedEvents := make([]*models.JumboV2TransformedEvent, 0, len(jeventPayloads))
	for _, jeventPayload := range jeventPayloads {
		eventData := map[string]interface{}{
			"source":     envelope.GetHeader().GetSource().String(),
			"device_id":  envelope.GetHeader().GetDeviceId().GetValue(),
			"session_id": envelope.GetHeader().GetSessionId().GetValue(),
			"user_id":    envelope.GetHeader().GetUserId().GetValue(),
			"user_agent": envelope.GetHeader().GetUserAgent().GetValue(),
			"timestamp":  envelope.GetHeader().GetTimestamp().AsTime().Format(time.RFC3339),
			"location":   fmt.Sprint(envelope.GetHeader().GetLocation().GetValue()),
			"ename":      jeventPayload.EventName,
			"key":        "jevent",
			"location_info": map[string]float64{
				"user_defined_latitude":  envelope.GetHeader().GetLocationInfo().GetUserDefinedLatitude().GetValue(),
				"user_defined_longitude": envelope.GetHeader().GetLocationInfo().GetUserDefinedLongitude().GetValue(),
				"current_longitude":      envelope.GetHeader().GetLocationInfo().GetCurrentLongitude().GetValue(),
				"current_latitude":       envelope.GetHeader().GetLocationInfo().GetCurrentLatitude().GetValue(),
			},
			"var1": jeventPayload.Var1,
			"var2": jeventPayload.Var2,
			"var3": jeventPayload.Var3,
			"var4": jeventPayload.Var4,
			"var5": jeventPayload.Var5,
			"var6": jeventPayload.Var6,
		}

		eventString, err := json.MarshalToString(eventData)
		if err != nil {
			logger.Error("[getO2CartTrackingEvents] Error in marshalling the ", zap.Error(err))
			continue
		}

		transformedEvents = append(transformedEvents, &models.JumboV2TransformedEvent{
			Payload: []byte(eventString),
			Table:   "jumbo_app_events",
			Key:     key,
			Topic:   "online_ordering.jumbo_app_events",
			Broker:  constants.DEFAULT_BROKER,
		})
	}

	return transformedEvents, err
}

func getPromoEvents(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &promoevents.PromoEvents{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to promo_events. Error: " + err.Error())
	}
	if !helpers.IsInStringSlice(pb.GetEventName().GetValue(), constants.PROMO_EVENTS_WHITELISTED_EVENT_NAMES) {
		logger.Infof("[getPromoEvents] Event %s is not required for transformation ", pb.GetEventName().GetValue())
		return nil, nil
	}

	return GetJumboV2FlattenedJSONMap(envelope, constants.ZOMATO_TENANT, key)
}

func getLogisticsAccountingEventLedger(ctx context.Context, envelope *jumboEvent.JumboEvent, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	pb := &logisticsaccountingeventledger.LogisticsAccountingEventLedger{}
	if err := ptypes.UnmarshalAny(envelope.Payload, pb); err != nil {
		return nil, errors.New("Unable to unmarshal any to LogisticsAccountingEventLedger event. Error: " + err.Error())
	}

	event := models.EventsLogisticsAccountingEventLedger{
		DriverId:         pb.GetDriverId(),
		RefId:            pb.GetRefId(),
		RefType:          pb.GetRefType(),
		Business:         pb.GetBusiness(),
		InternalOrderIds: pb.GetOrderIds(),
		CreatedAt:        pb.GetCreatedAt(),
		UpdatedAt:        pb.GetUpdatedAt(),
		Timestamp:        envelope.GetHeader().GetTimestamp().GetSeconds(),
	}

	isEstimated := pb.GetIsEstimated()
	amount := pb.GetAmount()
	if isEstimated {
		event.EstimatedAmount = &amount
		event.ActualAmount = nil
	} else {
		event.ActualAmount = &amount
		event.EstimatedAmount = nil
	}

	eventString, err := json.MarshalToString(event)
	if err != nil {
		logger.WithError(err).Error("unable to cast prepared payload map to json")
		return nil, err
	}

	tableKey := []byte(fmt.Sprint(pb.GetDriverId()) + pb.GetRefType() + pb.GetRefId())

	return []*models.JumboV2TransformedEvent{{
		Payload: []byte(eventString),
		Key:     tableKey,
		Table:   "logistics_accounting_event_ledger",
		Topic:   "insights.zomato.logistics_accounting_event_ledger",
		Broker:  constants.DEFAULT_BROKER,
	}}, err
}

func GetJumboV2FlattenedJSONMap(envelope *jumboEvent.JumboEvent, tenant string, key []byte) ([]*models.JumboV2TransformedEvent, error) {
	eventID := envelope.EventId
	eventSequenceID := envelope.SequenceId
	eventSequenceOffset := envelope.SequenceOffset
	eventTable := envelope.Table
	eventURL := envelope.Url

	marshaller := protojson.MarshalOptions{
		UseProtoNames: true,
	}

	tenantPayloadMap := map[string]map[string]proto.Message{
		constants.ZOMATO_TENANT: {
			"promo_events":                       &promoevents.PromoEvents{},
			"video_message_events":               &videomessageevents.VideoMessageEvents{},
			"xtreme_orders":                      &xtremeorders.XtremeOrders{},
			"weather_station_events":             &weatherstationevents.WeatherStationEvents{},
			"cell_logs_events":                   &celllogsevents.CellLogsEvents{},
			"order_rush_events":                  &orderrushevents.OrderRushEvents{},
			"res_serviceability_events":          &resServiceability.ResServiceabilityEvents{},
			"zfe_limit_transaction_events":       &zfelimittransactionevents.ZfeLimitTransactionEvents{},
			"chat_agent_metrics":                 &chatagentmetrics.ChatAgentMetrics{},
			"app_request_metrics":                &apprequestmetrics.AppRequestMetrics{},
			"app_error_metrics":                  &apperrormetrics.AppErrorMetrics{},
			"driver_wallet_breach_status_events": &driverwalletbreachstatusevents.DriverWalletBreachStatusEvents{},
			"unified_chat_events":                &unifiedchatevents.UnifiedChatEvents{},
			"web_request_metrics":                &webrequestmetrics.WebRequestMetrics{},
			"hermes_cell_speed_stats":            &hermescellspeedstats.HermesCellSpeedStats{},
			"hermes_driver_match_stats":          &hermesdrivermatchstats.HermesDriverMatchStats{},
			"unified_ticket_events":              &unifiedticketevents.UnifiedTicketEvents{},
			"one_support_events":                 &onesupportevents.OneSupportEvents{},
			"dining_tr_transaction_events":       &diningtrtransactionevents.DiningTrTransactionEvents{},
			"jadtracking":                        &jadtracking.JAdTracking{},
		},
		constants.BLINKIT_TENANT: {
			"click_events":                          &clickevents.ClickEvents{},
			"blinkit_web_events":                    &blinkitwebevents.BlinkitWebEvents{},
			"bistro_click_events":                   &bistroclickevents.BistroClickEvents{},
			"chat_agent_metrics":                    &chatagentmetrics.ChatAgentMetrics{},
			"warehouse_pick_list_events":            &warehousepicklistevents.WarehousePickListEvents{},
			"warehouse_put_list_events":             &warehouseputlistevents.WarehousePutListEvents{},
			"warehouse_order_events":                &warehouseorderevents.WarehouseOrderEvents{},
			"warehouse_packaging_activity_events":   &warehousepackagingactivityevents.WarehousePackagingActivityEvents{},
			"warehouse_segregation_events":          &warehousesegregationevents.WarehouseSegregationEvents{},
			"warehouse_unloading_task_events":       &warehouseunloadingtaskevents.WarehouseUnloadingTaskEvents{},
			"warehouse_outbound_container_events":   &warehouseoutboundcontainerevents.WarehouseOutboundContainerEvents{},
			"warehouse_emp_activity_tracker_events": &warehouseempactivitytrackerevents.WarehouseEmpActivityTrackerEvents{},
			"warehouse_plbv_events":                 &warehouseplbvevents.WarehousePlbvEvents{},
			"app_request_metrics":                   &apprequestmetrics.AppRequestMetrics{},
			"app_error_metrics":                     &apperrormetrics.AppErrorMetrics{},
			"bistro_order_feedback_events":          &bistroorderfeedbackevents.BistroOrderFeedbackEvents{},
			"sub_order_item_prep_details":           &suborderitemprepdetails.SubOrderItemPrepDetails{},
			"storeops_app_events":                   &storeopsappevents.StoreOpsAppEvents{},
			"unified_ticket_events":                 &unifiedticketevents.UnifiedTicketEvents{},
			"one_support_events":                    &onesupportevents.OneSupportEvents{},
		},
		constants.DISTRICT_TENANT: {
			"app_request_metrics":   &apprequestmetrics.AppRequestMetrics{},
			"app_error_metrics":     &apperrormetrics.AppErrorMetrics{},
			"web_request_metrics":   &webrequestmetrics.WebRequestMetrics{},
			"jadtracking":           &jadtracking.JAdTracking{},
			"unified_ticket_events": &unifiedticketevents.UnifiedTicketEvents{},
			"one_support_events":    &onesupportevents.OneSupportEvents{},
		},
		constants.NUGGET_TENANT: {
			"unified_ticket_events": &unifiedticketevents.UnifiedTicketEvents{},
			"one_support_events":    &onesupportevents.OneSupportEvents{},
		},
	}

	var payload proto.Message
	payload, ok := tenantPayloadMap[tenant][envelope.Table]
	if !ok {
		logger.Error("unable to find the table payload empty")
		return nil, nil
	}

	if err := anypb.UnmarshalTo(envelope.Payload, payload, proto.UnmarshalOptions{}); err != nil {
		logger.WithError(err).Error("unable to unmarshal any payload to the specific class")
		return nil, err
	}

	ingestion_timestamp := envelope.GetHeader().GetIngestionTime().GetSeconds()
	if ingestion_timestamp == 0 {
		ingestion_timestamp = time.Now().Unix()
	}

	data := map[string]interface{}{
		"source":              envelope.GetHeader().GetSource().String(),
		"device_id":           envelope.GetHeader().GetDeviceId().GetValue(),
		"session_id":          envelope.GetHeader().GetSessionId().GetValue(),
		"user_id":             envelope.GetHeader().GetUserId().GetValue(),
		"user_agent":          envelope.GetHeader().GetUserAgent().GetValue(),
		"timestamp":           envelope.GetHeader().GetTimestamp().GetSeconds(),
		"time":                envelope.GetHeader().GetTimestamp().GetSeconds(),
		"ingestion_timestamp": ingestion_timestamp,
		"location":            envelope.GetHeader().GetLocation().GetValue(),
		"app_info": map[string]interface{}{
			"device_performance": envelope.GetHeader().GetAppInfo().GetDevicePerformance().String(),
			"theme":              envelope.GetHeader().GetAppInfo().GetTheme().String(),
			"system_theme":       envelope.GetHeader().GetAppInfo().GetSystemTheme().String(),
			"app_appearance":     envelope.GetHeader().GetAppInfo().GetAppAppearance().String(),
		},
	}

	var eventPayloadJSONDto map[string]interface{}
	jsonString := marshaller.Format(payload)
	err := json.Unmarshal([]byte(jsonString), &eventPayloadJSONDto)
	if err != nil {
		logger.WithError(err).Error("unable to obtain json event header")
		return nil, err
	}

	for key, value := range eventPayloadJSONDto {
		// Check to skip key introduced by anypb
		if key == "@type" {
			continue
		}
		data[key] = value
	}

	// Step 4: Prepare the final Payload Map
	data["url"] = eventURL
	data["event_id"] = eventID
	data["sequence_id"] = eventSequenceID
	data["sequence_offset"] = eventSequenceOffset

	// Step 5: JSONify the prepared payload map
	finalPreparedEventJSON, err := json.Marshal(data)
	if err != nil {
		logger.WithError(err).Error("unable to cast prepared payload map to json")
		return nil, err
	}

	// Step 6: Prepare the transformed event
	return []*models.JumboV2TransformedEvent{{
		Payload: finalPreparedEventJSON,
		Key:     key,
		Table:   eventTable,
	}}, err
}
