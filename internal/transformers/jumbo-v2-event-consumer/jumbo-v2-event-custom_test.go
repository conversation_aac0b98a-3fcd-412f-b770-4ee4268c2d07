package jumbo_v2_event_consumer

import (
	"context"
	"testing"

	"github.com/Zomato/flash-gateway/internal/constants"
	"github.com/Zomato/flash-gateway/internal/models"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/productimageshownevents"
	jumboEvent "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/apigateway/o2carttracking"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

func Test_O2CartPageLoadedTrackingEvent(t *testing.T) {
	type args struct {
		envelope *jumboEvent.JumboEvent
		key      []byte
	}
	tests := []struct {
		name        string
		args        args
		want        []*models.JumboV2TransformedEvent
		wantErr     bool
		pb          *o2carttracking.O2CartTracking
		eventString string
	}{
		{
			name: "Test O2CartPageLoadedTrackingEvent",
			args: args{
				envelope: &jumboEvent.JumboEvent{
					EventId:  "123",
					Database: jumboEvent.Database_jumbo2,
					Header: &jumboEvent.Header{
						Source: jumboEvent.Source_android,
						DeviceId: &wrapperspb.StringValue{
							Value: "123456",
						},
						UserId: &wrapperspb.StringValue{
							Value: "1234567",
						},
						SessionId: &wrapperspb.StringValue{
							Value: "1234568",
						},
						UserAgent: &wrapperspb.StringValue{
							Value: "ios",
						},
						Timestamp: &timestamppb.Timestamp{
							Seconds: 1741175565,
						},
						Location: &wrapperspb.UInt32Value{
							Value: 1,
						},
						LocationInfo: &jumboEvent.LocationInfo{
							UserDefinedLatitude: &wrapperspb.DoubleValue{
								Value: 26.293832519647705,
							},
							UserDefinedLongitude: &wrapperspb.DoubleValue{
								Value: 73.04621815681458,
							},
							CurrentLatitude: &wrapperspb.DoubleValue{
								Value: 26.2937476,
							},
							CurrentLongitude: &wrapperspb.DoubleValue{
								Value: 73.0462254,
							},
						},
						IngestionTime: &timestamppb.Timestamp{
							Seconds: 1741175565,
						},
					},
				},
				key: []byte("123"),
			},
			want: []*models.JumboV2TransformedEvent{
				{
					Table:  "jumbo_app_events",
					Key:    []byte("123"),
					Topic:  "online_ordering.jumbo_app_events",
					Broker: constants.DEFAULT_BROKER,
				},
			},
			wantErr: false,
			pb: &o2carttracking.O2CartTracking{
				EventName: &wrapperspb.StringValue{
					Value: "O2CartPageLoaded",
				},
				CartId: &wrapperspb.StringValue{
					Value: "1234567",
				},
				CartSequenceId: &wrapperspb.StringValue{
					Value: "1234568",
				},
				ResIds: []*wrapperspb.StringValue{
					{
						Value: "302115",
					},
				},
				DeliveryAddress: &o2carttracking.AddressDetails{
					AddressId: &wrapperspb.StringValue{
						Value: "66666",
					},
					PlaceId: &wrapperspb.StringValue{
						Value: "1",
					},
					PlaceType: &wrapperspb.StringValue{
						Value: "4",
					},
				},
				PaymentDetails: &o2carttracking.PaymentDetails{
					PaymentMethodType: &wrapperspb.StringValue{
						Value: "upi",
					},
				},
			},
			eventString: "{\"device_id\":\"123456\",\"ename\":\"O2CartPageLoaded\",\"key\":\"jevent\",\"location\":\"1\",\"location_info\":{\"current_latitude\":26.2937476,\"current_longitude\":73.0462254,\"user_defined_latitude\":26.293832519647705,\"user_defined_longitude\":73.04621815681458},\"session_id\":\"1234568\",\"source\":\"android\",\"timestamp\":\"2025-03-05T11:52:45Z\",\"user_agent\":\"ios\",\"user_id\":\"1234567\",\"var1\":\"302115\",\"var2\":\"0\",\"var3\":\"upi\",\"var4\":\"66666\",\"var5\":\"1\",\"var6\":\"4\"}",
		},
	}
	for _, tt := range tests {
		pbAny, err := anypb.New(tt.pb)
		if err != nil {
			t.Errorf("Error in creating anypb: %+v", err)
		}
		tt.args.envelope.Payload = pbAny
		tt.want[0].Payload = []byte(tt.eventString)

		t.Run(tt.name, func(t *testing.T) {
			got, err := getO2CartTrackingEvents(context.Background(), tt.args.envelope, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("getO2CartTrackingEvents() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_getProductImageShownEvents(t *testing.T) {
	type args struct {
		envelope *jumboEvent.JumboEvent
		key      []byte
	}
	tests := []struct {
		name        string
		args        args
		want        []*models.JumboV2TransformedEvent
		wantErr     bool
		pb          *productimageshownevents.ProductImageShownEvents
		eventString string
	}{
		{
			name: "Test ProductImageShownEvents",
			args: args{
				envelope: &jumboEvent.JumboEvent{
					EventId:  "test-product-image-event",
					Database: jumboEvent.Database_jumbo2,
					Table:    "product_image_shown_events",
					Header: &jumboEvent.Header{
						Source: jumboEvent.Source_android,
						DeviceId: &wrapperspb.StringValue{
							Value: "test-device-123",
						},
						UserId: &wrapperspb.StringValue{
							Value: "test-user-456",
						},
						SessionId: &wrapperspb.StringValue{
							Value: "test-session-789",
						},
						UserAgent: &wrapperspb.StringValue{
							Value: "android-app",
						},
						Timestamp: &timestamppb.Timestamp{
							Seconds: 1641175565,
						},
						Location: &wrapperspb.UInt32Value{
							Value: 1,
						},
						IngestionTime: &timestamppb.Timestamp{
							Seconds: 1641175566,
						},
					},
				},
				key: []byte("test-key"),
			},
			pb: &productimageshownevents.ProductImageShownEvents{
				EventName: &wrapperspb.StringValue{
					Value: "product_image_shown",
				},
				Traits: &productimageshownevents.Traits{
					AppFlavor: &wrapperspb.StringValue{
						Value: "consumer",
					},
					CartId: &wrapperspb.StringValue{
						Value: "cart-123",
					},
					ChainId: &wrapperspb.Int32Value{
						Value: 1,
					},
					Channel: &wrapperspb.StringValue{
						Value: "android",
					},
					CityId: &wrapperspb.Int32Value{
						Value: 1,
					},
					CityName: &wrapperspb.StringValue{
						Value: "Delhi",
					},
					MerchantId: &wrapperspb.Int32Value{
						Value: 12345,
					},
					MerchantName: &wrapperspb.StringValue{
						Value: "Test Store",
					},
					UserType: &wrapperspb.StringValue{
						Value: "regular",
					},
				},
				Properties: &productimageshownevents.Properties{
					ProductId: &wrapperspb.Int64Value{
						Value: 98765,
					},
					Name: &wrapperspb.StringValue{
						Value: "Test Product",
					},
					Brand: &wrapperspb.StringValue{
						Value: "Test Brand",
					},
					Price: &wrapperspb.DoubleValue{
						Value: 299.99,
					},
					L0Category: &wrapperspb.StringValue{
						Value: "Electronics",
					},
					L1Category: &wrapperspb.StringValue{
						Value: "Mobile",
					},
					PageName: &wrapperspb.StringValue{
						Value: "product_listing",
					},
					WidgetName: &wrapperspb.StringValue{
						Value: "product_card",
					},
					WidgetPosition: &wrapperspb.Int32Value{
						Value: 1,
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Marshal the protobuf message
			pbAny, err := anypb.New(tt.pb)
			assert.NoError(t, err)
			tt.args.envelope.Payload = pbAny

			// Call the function
			got, err := getProductImageShownEvents(context.Background(), tt.args.envelope, tt.args.key)

			// Check for errors
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)

			// Verify result
			assert.Len(t, got, 1)
			assert.Equal(t, tt.args.key, got[0].Key)
			assert.Equal(t, tt.args.envelope.GetTable(), got[0].Table)
			assert.NotEmpty(t, got[0].Payload)

			// Verify that payload can be unmarshaled back to JSON
			assert.NotPanics(t, func() {
				var result map[string]interface{}
				err := json.Unmarshal(got[0].Payload, &result)
				assert.NoError(t, err)

				// Check some key fields
				assert.Equal(t, "product_image_shown", result["event_name"])
				assert.Equal(t, "test-device-123", result["device_id"])
				assert.Equal(t, "test-user-456", result["user_id"])
				assert.Equal(t, "test-session-789", result["session_id"])
				assert.Equal(t, float64(98765), result["product_id"])
				assert.Equal(t, "Test Product", result["name"])
				assert.Equal(t, "Test Brand", result["brand"])
				assert.Equal(t, 299.99, result["price"])
			})
		})
	}
}
