package jumbo_v2_event_consumer

import (
	"context"
	stdjson "encoding/json"
	"testing"

	"github.com/Zomato/flash-gateway/internal/constants"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/productimageshownevents"
	jumboEvent "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

// Sample payload data based on your optimisation-insights/sample_events/product_image_shown_events.json
var sampleProductImageEvent = &productimageshownevents.ProductImageShownEvents{
	EventName: &wrapperspb.StringValue{Value: "Image Shown"},
	Traits: &productimageshownevents.Traits{
		AppFlavor:      &wrapperspb.StringValue{Value: "NORMAL"},
		AppVersionCode: &wrapperspb.Int64Value{Value: 172200},
		ChainId:        &wrapperspb.Int32Value{Value: 1383},
		Channel:        &wrapperspb.StringValue{Value: "BLINKIT"},
		CityId:         &wrapperspb.Int32Value{Value: 3},
		CityName:       &wrapperspb.StringValue{Value: "Bengaluru"},
		DeviceUuid:     &wrapperspb.StringValue{Value: "E8CBC5A9-30F1-4C97-BC7F-36E4DDC09F8B"},
		Latitude:       &wrapperspb.DoubleValue{Value: 12.954776022061054},
		Longitude:      &wrapperspb.DoubleValue{Value: 77.61588397170428},
		MerchantId:     &wrapperspb.Int32Value{Value: 30377},
		LifetimeOrders: &wrapperspb.Int32Value{Value: 36},
		MonthlyOrders:  &wrapperspb.DoubleValue{Value: 7},
		UserType:       &wrapperspb.StringValue{Value: "ACTIVE"},
		// Complex segment features array
		SegmentEnabledFeatures: []*wrapperspb.StringValue{
			{Value: "slp-weighted-ranking-equation:0"},
			{Value: "show-sort-slp"},
			{Value: "ads-service-flow-rollout:baelish"},
			{Value: "back-in-stock-grid:enabled"},
			{Value: "cbf-similarity-enabled"},
			{Value: "cbf-dismiss-button-enabled"},
			{Value: "see-all-on-slp"},
			{Value: "see-all-recipe-on-plp"},
			{Value: "ads-collection-inventory-check-migration"},
			{Value: "semantic-search"},
			{Value: "slp-3-cards"},
			{Value: "autosuggest-keyword-ranking:0"},
			{Value: "paas-cart-merge-enabled"},
			{Value: "plp-tobacco-consent:enable"},
			{Value: "top-brands:3"},
			{Value: "pin-lq-products"},
			{Value: "pecos"},
			{Value: "generated-tags-autosuggest"},
			{Value: "continue-browsing-for:enable"},
			{Value: "autocomplete-prefix-match-variation:default"},
			{Value: "mandatory-autosuggest"},
			{Value: "feed-recipe-v2-rollout:enable"},
			{Value: "oos-widget-on-slp"},
			{Value: "search-show-duplicate-autosuggest"},
			{Value: "enable-usecase-container"},
			{Value: "search-autosuggest-tap-vs-type:1"},
			{Value: "enable-empty-search-v2"},
			{Value: "low-converting-keywords-boosting"},
			{Value: "city-sales-ranking-v2"},
			{Value: "ad-density-variation:0"},
			{Value: "product-suggestion"},
			{Value: "generated-tag-variation:1"},
			{Value: "search-similarity-model"},
			{Value: "keyterm-similarity-variation:1"},
			{Value: "show-user-query-keyword-autosuggestion"},
			{Value: "search-instead-for"},
			{Value: "incomplete-words-boosting"},
			{Value: "recommendations-variation:1"},
			{Value: "variant-selection-pop-up-v2:bottom_sheet_with_tag"},
			{Value: "item_rail_v2"},
			{Value: "show-images-in-autosuggest"},
			{Value: "attribute-personalization:2"},
			{Value: "location-info-reverse-geocode-service:sauron"},
			{Value: "pdp-recommendation:fetch_related_products_from_espina"},
			{Value: "on-demand-merchandising"},
			{Value: "use-sales-city-level-data"},
			{Value: "should-use-constant-scoring"},
			{Value: "use-autosuggest-as-intent"},
			{Value: "category-filtering"},
			{Value: "use-ner"},
			{Value: "autosuggest-incomplete-spellcorrect"},
			{Value: "sponsored-banners-v2"},
			{Value: "autosuggest-variation:1"},
			{Value: "search-models-variations:1"},
			{Value: "show-usecases"},
			{Value: "keyword-recipe-position-decider:keyword_first"},
			{Value: "inline-filters-visibility"},
			{Value: "show-similar-products"},
			{Value: "user-bucket:C"},
			{Value: "category_reorder_on_feed_experiment:2_0_0"},
			{Value: "search-within-plp"},
			{Value: "buy-more-save-more:b3c61c"},
			{Value: "new-er-scoring-logic"},
			{Value: "use-sales-data"},
			{Value: "affluence-experiment"},
			{Value: "autocomplete-variation:0"},
			{Value: "use-spellcorrect-model"},
			{Value: "sponsored-products-boost:1"},
			{Value: "few-left-exp:bf21a9"},
			{Value: "use-primary-secondary-merged-flow-in-type-to-search"},
			{Value: "category-grid-old-user-rows:5"},
			{Value: "plp-instamart:vertical-plp"},
			{Value: "type-to-search"},
			{Value: "search-keyterm-similarity"},
			{Value: "search-city-level-autosuggest"},
			{Value: "global-collection"},
			{Value: "fastest_delivery_vs_eta:control"},
			{Value: "top-seller-nu-city-wise:control_top_seller"},
			{Value: "households-served-experiment:control_households"},
			{Value: "is-cart-footer-strip-shown"},
			{Value: "bill-buster-experiment:billbuster_control"},
			{Value: "express-allowed-user-segment"},
			{Value: "express-merchant-delivery-threshold:-1"},
			{Value: "exp-feed-carousel-with-variations:3000"},
			{Value: "plp-promo-banner:bf21a9"},
			{Value: "google_maps"},
			{Value: "map_my_india"},
			{Value: "quick-cart-enabled"},
			{Value: "show-dynamic-paan-corner-banner"},
			{Value: "show-search-filters"},
			{Value: "track-order-unified-rating-experience-rollout"},
			{Value: "show-kitchen-tools-rail"},
			{Value: "track-order-v-3-enabled"},
			{Value: "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag"},
			{Value: "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag"},
			{Value: "special-groups-for-goa:fd7a20"},
			{Value: "smart-basket-version:v1"},
			{Value: "intelligent-pb-version:v2"},
			{Value: "discover-new-finds:enabled"},
			{Value: "dnf-boosting:"},
			{Value: "byc-boosting-config:fd30b6"},
			{Value: "npr-migration"},
			{Value: "dynamic-curated-for-you-rollout"},
			{Value: "pdp-similar-items-v2"},
			{Value: "npr-version:v8"},
			{Value: "rollout-tod-best-sellers"},
			{Value: "app-v-16-rollout-enabled"},
			{Value: "post-checkout-scratch-card-rollout"},
			{Value: "pdp-similar-products-ab"},
			{Value: "attribute-rails:3"},
			{Value: "cms-filters-rollout"},
			{Value: "pin-new-products"},
			{Value: "pb-propensity"},
			{Value: "post-checkout-ios-type-6-snippet-migration-enabled"},
			{Value: "enable_import_z_address_on_cart"},
			{Value: "new-user-ranking"},
			{Value: "consumer-app-international-phone-login-enabled"},
			{Value: "use-query-to-product"},
			{Value: "dc-rail"},
			{Value: "aspirational-card-rail"},
			{Value: "byc-pb-ptype-logic"},
			{Value: "consumer-app-pdp-switcher-enabled"},
			{Value: "slp-trending-behaviour:3"},
			{Value: "empty-search-rollout"},
			{Value: "dc-realtime-sorting-config:bf21a9"},
			{Value: "cart-master-ptype-based-deboosting-on-npr"},
			{Value: "reduce-precision-set-byc"},
			{Value: "mv-instant-enabled"},
			{Value: "empty-search-trending-keyterms:enabled"},
			{Value: "enable-blinkit-money"},
			{Value: "dark-mode-rollout-enabled"},
			{Value: "high-confident-pb-ranking"},
			{Value: "use-butterfly-model"},
			{Value: "pb-intelligent-scoring-version:v2"},
			{Value: "use-product-sales-score"},
			{Value: "new-text-match-qtp-scoring"},
			{Value: "is_variant_compression_enabled"},
			{Value: "is-primary-config-endpoint-enabled"},
			{Value: "post-checkout-crystal-bff-migration-rollout-enabled"},
			{Value: "track-order-v2-aerobar-version-rollout"},
			{Value: "consumer-app-wishlist-feature-enabled"},
			{Value: "butterfly-spellcorrect-ab"},
			{Value: "use-butterfly-spellcorrect"},
			{Value: "location-permission-popup-v2-enabled"},
			{Value: "enable-pharma-flow"},
			{Value: "location-autosuggest-backend-enabled"},
			{Value: "consumer-app-web-view-auth-token-flow-enabled"},
			{Value: "filters_v3"},
			{Value: "use-pds-product-id-products"},
			{Value: "consumer-app-feeding-india-impact-page-rollout-enabled"},
			{Value: "far_away_flow_profile"},
			{Value: "far_away_flow_cart"},
		},
		// Complex segment types array
		SegmentType: []*wrapperspb.StringValue{
			{Value: "high_aov_segment_dump_new_def"},
			{Value: "lux_brands_segment"},
			{Value: "baby_care_segment"},
			{Value: "electronics_lapsed_segment"},
			{Value: "ntc_electronics_and_electricals"},
			{Value: "stationary_buyers"},
			{Value: "harpic_user"},
			{Value: "premium_ull_flag"},
			{Value: "segment_hair_care"},
			{Value: "health_fitness"},
			{Value: "ads_diy_baby_buy"},
			{Value: "ads_diy_personal_care_buy"},
			{Value: "chocolate_munchies_biscuit_premium"},
			{Value: "ads_kids"},
			{Value: "repellents_user"},
			{Value: "ads_diy_grocery_buy"},
			{Value: "ads_female"},
			{Value: "plum_potential_user"},
			{Value: "razor_premium"},
			{Value: "fem_care_user"},
			{Value: "ads_diy_beauty_cosmetics_buy"},
			{Value: "top_twenty_purchaser"},
			{Value: "soulflower_new_potential"},
			{Value: "grocery_user"},
			{Value: "baby_buyers_6"},
			{Value: "sexual_wellness_potential"},
			{Value: "beauty_customers"},
			{Value: "diswashing_gel_sampling"},
			{Value: "personal_care_user"},
			{Value: "party_esttential_potential"},
			{Value: "batter_cohort"},
			{Value: "chicconew_auto"},
			{Value: "blr_dro_jigsaw"},
			{Value: "babycareuser_cdp"},
			{Value: "ASBL_Flyer"},
			{Value: "personalisation_premium_user_segment"},
			{Value: "navratna_bpc"},
			{Value: "personalisation_kids_cohort_high"},
			{Value: "Users_Female"},
			{Value: "femcare_user_whisper"},
			{Value: "personalisation_non_vegetarian_cohort_medium"},
			{Value: "batter_event_cohort_blr"},
			{Value: "babyusersminus_mamaearth"},
			{Value: "beautypersonalcare_cdp"},
			{Value: "navratnasampling_cdp"},
			{Value: "personalisation_toddler_infant_cohort_high"},
			{Value: "mccainrakhiflyer_user"},
			{Value: "segment_z_blinkit75_offer"},
			{Value: "segment_z_blinkit_coke_offer_v4"},
		},
		UserExperimentBuckets: []*wrapperspb.StringValue{
			{Value: "RANDOM#bucket2"},
			{Value: "INCREASING_ROLLOUT#bucket2"},
			{Value: "DECREASING_ROLLOUT#bucket2"},
		},
	},
	Properties: &productimageshownevents.Properties{
		Badge:                  &wrapperspb.StringValue{Value: "savings"},
		Brand:                  &wrapperspb.StringValue{Value: "Elle 18"},
		CardType:               &wrapperspb.StringValue{Value: "variant-selection"},
		CartId:                 &wrapperspb.StringValue{Value: "-1"},
		ChildWidgetPosition:    &wrapperspb.Int32Value{Value: 0},
		Currency:               &wrapperspb.StringValue{Value: "INR"},
		EtaIdentifier:          &wrapperspb.StringValue{Value: "express"},
		FiltersPresent:         &wrapperspb.StringValue{Value: "relevance;"},
		HighlightIds:           &wrapperspb.StringValue{Value: "80,758"},
		IconType:               &wrapperspb.StringValue{Value: "wishlist"},
		Inventory:              &wrapperspb.Int32Value{Value: 2},
		InventoryLimit:         &wrapperspb.Int32Value{Value: 2},
		IsTopRightIconSelected: &wrapperspb.BoolValue{Value: false},
		L0Category:             &wrapperspb.StringValue{Value: ""},
		L1Category:             &wrapperspb.StringValue{Value: ""},
		L2Category:             &wrapperspb.StringValue{Value: "Lipsticks"},
		LastPageId:             &wrapperspb.StringValue{Value: "155"},
		LastPageName:           &wrapperspb.StringValue{Value: "feed"},
		LastPageTitle:          &wrapperspb.StringValue{Value: "feed_default"},
		LastPageVisitId:        &wrapperspb.StringValue{Value: "0FEED0F0-7CC3-4D6A-B12D-AE04AEB70A2B"},
		Latitude:               &wrapperspb.DoubleValue{Value: 12.954776022061054},
		Longitude:              &wrapperspb.DoubleValue{Value: 77.61588397170428},
		MerchantId:             &wrapperspb.Int32Value{Value: 30377},
		MerchantType:           &wrapperspb.StringValue{Value: "express"},
		Mrp:                    &wrapperspb.Int32Value{Value: 160},
		Name:                   &wrapperspb.StringValue{Value: "Elle 18 Matte Liquid Lip Color (Nude Pump)"},
		PageId:                 &wrapperspb.StringValue{Value: "OTg3NjU0MzIxMjM0NTMzNDE="},
		PageName:               &wrapperspb.StringValue{Value: "listing_widgets"},
		PageTitle:              &wrapperspb.StringValue{Value: "Listing"},
		PageTrackingId:         &wrapperspb.StringValue{Value: "#-NA"},
		PageType:               &wrapperspb.StringValue{Value: "Category"},
		PageVisitId:            &wrapperspb.StringValue{Value: "GSplitViewController-1293E119-B0EB-4934-9F66-9B8D61A4C759"},
		Price:                  &wrapperspb.DoubleValue{Value: 144},
		ProductId:              &wrapperspb.Int64Value{Value: 499655},
		ProductListId:          &wrapperspb.StringValue{Value: "OTg3NjU0MzIxMjM0NTMzNDE="},
		ProductOffers:          &wrapperspb.StringValue{Value: "percentage_off"},
		ProductPosition:        &wrapperspb.Int32Value{Value: 8},
		Ptype:                  &wrapperspb.StringValue{Value: "Lip Color"},
		Quantity:               &wrapperspb.Int32Value{Value: 1},
		Rating:                 &wrapperspb.StringValue{Value: "3.716"},
		State:                  &wrapperspb.StringValue{Value: "available"},
		SubcategoryId:          &wrapperspb.StringValue{Value: "2411"},
		TimeToDeliveryInMins:   &wrapperspb.DoubleValue{Value: 8},
		Title:                  &wrapperspb.StringValue{Value: "Elle 18 Matte Liquid Lip Color (Nude Pump)"},
		WidgetId:               &wrapperspb.StringValue{Value: "499655"},
		WidgetImpressionCount:  &wrapperspb.Int32Value{Value: 1},
		WidgetName:             &wrapperspb.StringValue{Value: "Product"},
		WidgetPosition:         &wrapperspb.Int32Value{Value: 8},
		WidgetTitle:            &wrapperspb.StringValue{Value: "Elle 18 Matte Liquid Lip Color (Nude Pump)"},
		WidgetVariationId:      &wrapperspb.StringValue{Value: "global_product_listing"},
		WishlistAdded:          &wrapperspb.BoolValue{Value: false},
	},
}

func createProductImageEventEnvelope(tb testing.TB) (*jumboEvent.JumboEvent, []byte) {
	pbAny, err := anypb.New(sampleProductImageEvent)
	if err != nil {
		tb.Fatal(err)
	}

	envelope := &jumboEvent.JumboEvent{
		EventId:  "benchmark-product-image-event",
		Database: jumboEvent.Database_jumbo2,
		Table:    "product_image_shown_events",
		Payload:  pbAny,
		Header: &jumboEvent.Header{
			Source:        jumboEvent.Source_ios,
			DeviceId:      &wrapperspb.StringValue{Value: "E8CBC5A9-30F1-4C97-BC7F-36E4DDC09F8B"},
			UserId:        &wrapperspb.StringValue{Value: "31442899"},
			SessionId:     &wrapperspb.StringValue{Value: "C2080F5A-CF06-4E1D-A0C8-248E100B2A6D"},
			UserAgent:     &wrapperspb.StringValue{Value: "&source=ios&sdk_version=1.0&device_manufacturer=Apple&device_model=iPhone+12+Pro+Max&app_version=17.22.0&version=18.1&network_type=WIFI&lang=en"},
			Timestamp:     &timestamppb.Timestamp{Seconds: 1756416428},
			Location:      &wrapperspb.UInt32Value{Value: 1},
			IngestionTime: &timestamppb.Timestamp{Seconds: 1756416435},
		},
	}

	key := []byte("31442899")
	return envelope, key
}

// Benchmark the CUSTOM transformer for product_image_shown_events
func BenchmarkCustomProductImageShownTransformer(b *testing.B) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constants.JobNameCtxKey, "benchmark-test")

	envelope, key := createProductImageEventEnvelope(b)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, err := getProductImageShownEvents(ctx, envelope, key)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Benchmark the GENERIC transformer for product_image_shown_events
func BenchmarkGenericProductImageShownTransformer(b *testing.B) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constants.JobNameCtxKey, "benchmark-test")

	envelope, key := createProductImageEventEnvelope(b)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, err := GetJumboV2FlattenedJSONMap(envelope, constants.BLINKIT_TENANT, key)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Parallel benchmarks
func BenchmarkCustomProductImageShownTransformerParallel(b *testing.B) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constants.JobNameCtxKey, "benchmark-test")

	envelope, key := createProductImageEventEnvelope(b)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := getProductImageShownEvents(ctx, envelope, key)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

func BenchmarkGenericProductImageShownTransformerParallel(b *testing.B) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constants.JobNameCtxKey, "benchmark-test")

	envelope, key := createProductImageEventEnvelope(b)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := GetJumboV2FlattenedJSONMap(envelope, constants.BLINKIT_TENANT, key)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// Benchmark with different array sizes to simulate varying segment complexity
func BenchmarkTransformersWithSegmentComplexity(b *testing.B) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constants.JobNameCtxKey, "benchmark-test")

	testCases := []struct {
		name                   string
		segmentFeaturesCount   int
		segmentTypesCount      int
		experimentBucketsCount int
	}{
		{"SmallSegments", 10, 5, 2},
		{"MediumSegments", 50, 20, 5},
		{"LargeSegments", 150, 50, 10},
		{"XLargeSegments", 300, 100, 20}, // Close to real production data
	}

	for _, tc := range testCases {
		// Create test event with specified complexity
		testEvent := &productimageshownevents.ProductImageShownEvents{
			EventName: &wrapperspb.StringValue{Value: "Image Shown"},
			Traits: &productimageshownevents.Traits{
				AppFlavor:              &wrapperspb.StringValue{Value: "NORMAL"},
				ChainId:                &wrapperspb.Int32Value{Value: 1383},
				Channel:                &wrapperspb.StringValue{Value: "BLINKIT"},
				CityId:                 &wrapperspb.Int32Value{Value: 3},
				MerchantId:             &wrapperspb.Int32Value{Value: 30377},
				SegmentEnabledFeatures: make([]*wrapperspb.StringValue, tc.segmentFeaturesCount),
				SegmentType:            make([]*wrapperspb.StringValue, tc.segmentTypesCount),
				UserExperimentBuckets:  make([]*wrapperspb.StringValue, tc.experimentBucketsCount),
			},
			Properties: &productimageshownevents.Properties{
				ProductId:  &wrapperspb.Int64Value{Value: 499655},
				Name:       &wrapperspb.StringValue{Value: "Test Product"},
				Brand:      &wrapperspb.StringValue{Value: "Test Brand"},
				Price:      &wrapperspb.DoubleValue{Value: 144},
				MerchantId: &wrapperspb.Int32Value{Value: 30377},
			},
		}

		// Fill arrays with test data
		for i := 0; i < tc.segmentFeaturesCount; i++ {
			testEvent.Traits.SegmentEnabledFeatures[i] = &wrapperspb.StringValue{
				Value: "feature-" + string(rune(i+48)), // Simple feature names
			}
		}
		for i := 0; i < tc.segmentTypesCount; i++ {
			testEvent.Traits.SegmentType[i] = &wrapperspb.StringValue{
				Value: "segment-" + string(rune(i+48)),
			}
		}
		for i := 0; i < tc.experimentBucketsCount; i++ {
			testEvent.Traits.UserExperimentBuckets[i] = &wrapperspb.StringValue{
				Value: "bucket-" + string(rune(i+48)),
			}
		}

		pbAny, err := anypb.New(testEvent)
		if err != nil {
			b.Fatal(err)
		}

		envelope := &jumboEvent.JumboEvent{
			EventId:  "benchmark-complexity-test",
			Database: jumboEvent.Database_jumbo2,
			Table:    "product_image_shown_events",
			Payload:  pbAny,
			Header: &jumboEvent.Header{
				Source:    jumboEvent.Source_ios,
				DeviceId:  &wrapperspb.StringValue{Value: "test-device"},
				UserId:    &wrapperspb.StringValue{Value: "test-user"},
				Timestamp: &timestamppb.Timestamp{Seconds: 1756416428},
			},
		}

		key := []byte("test-key")

		b.Run(tc.name+"_Custom", func(b *testing.B) {
			b.ResetTimer()
			b.ReportAllocs()
			for i := 0; i < b.N; i++ {
				_, err := getProductImageShownEvents(ctx, envelope, key)
				if err != nil {
					b.Fatal(err)
				}
			}
		})

		b.Run(tc.name+"_Generic", func(b *testing.B) {
			b.ResetTimer()
			b.ReportAllocs()
			for i := 0; i < b.N; i++ {
				_, err := GetJumboV2FlattenedJSONMap(envelope, constants.BLINKIT_TENANT, key)
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	}
}

// Memory-focused benchmark
func BenchmarkMemoryAllocation(b *testing.B) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constants.JobNameCtxKey, "benchmark-test")

	envelope, key := createProductImageEventEnvelope(b)

	b.Run("Custom_Memory", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := getProductImageShownEvents(ctx, envelope, key)
			if err != nil {
				b.Fatal(err)
			}
			// Verify we got a result to prevent optimization
			if len(result) == 0 {
				b.Fatal("Expected non-empty result")
			}
		}
	})

	b.Run("Generic_Memory", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := GetJumboV2FlattenedJSONMap(envelope, constants.BLINKIT_TENANT, key)
			if err != nil {
				b.Fatal(err)
			}
			// Verify we got a result to prevent optimization
			if len(result) == 0 {
				b.Fatal("Expected non-empty result")
			}
		}
	})
}

// JSON marshaling benchmark (likely bottleneck)
func BenchmarkJSONSerialization(b *testing.B) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constants.JobNameCtxKey, "benchmark-test")

	envelope, key := createProductImageEventEnvelope(b)

	// Get the transformed results first
	customResult, err := getProductImageShownEvents(ctx, envelope, key)
	if err != nil {
		b.Fatal(err)
	}

	genericResult, err := GetJumboV2FlattenedJSONMap(envelope, constants.BLINKIT_TENANT, key)
	if err != nil {
		b.Fatal(err)
	}

	b.Run("Custom_JSON_Size", func(b *testing.B) {
		if len(customResult) > 0 {
			b.Logf("Custom result payload size: %d bytes", len(customResult[0].Payload))
		}
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Benchmark just the final JSON marshaling step
			var testData map[string]interface{}
			if len(customResult) > 0 {
				stdjson.Unmarshal(customResult[0].Payload, &testData)
				stdjson.Marshal(testData)
			}
		}
	})

	b.Run("Generic_JSON_Size", func(b *testing.B) {
		if len(genericResult) > 0 {
			b.Logf("Generic result payload size: %d bytes", len(genericResult[0].Payload))
		}
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Benchmark just the final JSON marshaling step
			var testData map[string]interface{}
			if len(genericResult) > 0 {
				stdjson.Unmarshal(genericResult[0].Payload, &testData)
				stdjson.Marshal(testData)
			}
		}
	})
}

// CPU profiling benchmark
func BenchmarkCPUIntensive(b *testing.B) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constants.JobNameCtxKey, "benchmark-test")

	envelope, key := createProductImageEventEnvelope(b)

	b.Run("Custom_CPU", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			for j := 0; j < 100; j++ { // Simulate high-throughput processing
				_, err := getProductImageShownEvents(ctx, envelope, key)
				if err != nil {
					b.Fatal(err)
				}
			}
		}
	})

	b.Run("Generic_CPU", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			for j := 0; j < 100; j++ { // Simulate high-throughput processing
				_, err := GetJumboV2FlattenedJSONMap(envelope, constants.BLINKIT_TENANT, key)
				if err != nil {
					b.Fatal(err)
				}
			}
		}
	})
}
