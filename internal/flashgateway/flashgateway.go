package flashgateway

import (
	"context"
	"fmt"

	"github.com/Zomato/flash-gateway/internal/constants"
	"github.com/Zomato/flash-gateway/internal/env"
	"github.com/Zomato/flash-gateway/internal/handlers"
	"github.com/Zomato/flash-gateway/internal/helpers"

	"github.com/Zomato/flash-gateway/pkg/services/cache"
	"github.com/Zomato/flash-gateway/pkg/services/clevertap"
	"github.com/Zomato/flash-gateway/pkg/services/kafka"
	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"

	"go.uber.org/zap"
)

// Start runs flash gateway
func Start(ctx context.Context, jobtype string) {
	if jobtype == "" {
		log.Errorf("Unable to set consumer claim function. Empty jobtype")
	}

	var kafkaConsumerLabel string

	ctx = context.WithValue(ctx, constants.JobNameCtxKey, jobtype)

	claim := handlers.NewClaim(ctx)
	claim.JobType = jobtype

	switch jobtype {
	case constants.JOBTYPE_FLASH_CONSUMER:
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.FlashConsumerClaimFunc))
		kafkaConsumerLabel = "aggr"
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
	case constants.JOBTYPE_JUMBO_V2_CONSUMER:
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.JumboV2ConsumerClaimFunc))
		kafkaConsumerLabel = "jumbov2"
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
	case constants.JOBTYPE_JUMBO_CONSUMER_FRONTEND:
		helpers.TablesInit("default", ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ZJumboConsumerClaimFuncFrontend))
		kafkaConsumerLabel = constants.ZOMATO_JUMBO_CONSUMER_FRONTEND
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
	case constants.JOBTYPE_BLINKIT_JUMBO_CONSUMER_FRONTEND:
		helpers.TablesInit(constants.BLINKIT_TENANT, ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.BlinkitJumboConsumerClaimFuncFrontend))
		kafkaConsumerLabel = constants.BLINKIT_JUMBO_CONSUMER_FRONTEND
		helpers.AddProducerToSetup(constants.BLINKIT_TENANT)
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
	case constants.JOBTYPE_DISTRICT_JUMBO_CONSUMER:
		helpers.TablesInit(constants.DISTRICT_TENANT, ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.DistrictJumboConsumerClaimFuncFrontend))
		kafkaConsumerLabel = constants.DISTRICT_TENANT
		helpers.AddProducerToSetup(constants.DISTRICT_TENANT)
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
	case constants.JOBTYPE_NUGGET_JUMBO_CONSUMER_FRONTEND:
		helpers.TablesInit(constants.NUGGET_TENANT, ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.NuggetJumboConsumerClaimFuncFrontend))
		kafkaConsumerLabel = constants.JOBTYPE_NUGGET_JUMBO_CONSUMER_FRONTEND
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)

	case constants.JOBTYPE_JUMBO_CONSUMER_BACKEND:
		helpers.TablesInit("default", ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ZJumboConsumerClaimFuncBackend))
		kafkaConsumerLabel = constants.ZOMATO_JUMBO_CONSUMER_BACKEND
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
	case constants.JOBTYPE_BLINKIT_JUMBO_CONSUMER_BACKEND:
		helpers.TablesInit(constants.BLINKIT_TENANT, ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.BlinkitJumboConsumerClaimFuncBackend))
		kafkaConsumerLabel = constants.BLINKIT_JUMBO_CONSUMER_BACKEND
		helpers.AddProducerToSetup(constants.BLINKIT_TENANT)
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)

	case constants.JOBTYPE_ENRICHER:
		cache.Initialize(ctx)
		err := helpers.SetupEnricher()
		if err != nil {
			log.Error("Unable to start enricher", zap.String("error", err.Error()))
		}
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.EnricherConsumeClaimFunc))
		kafkaConsumerLabel = "flash"
		helpers.AddProducerToSetup(config.GetString(ctx, "enricher.kafka"))
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
	case constants.JOBTYPE_ENRICHMENT_INGESTOR:
		cache.Initialize(ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.EnrichmentIngestorClaimFuncV2(ctx)))
		kafkaConsumerLabel = "enrichmentingestor"
	case constants.JOBTYPE_ALERTING_CONSUMER:
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.AlertingConsumerClaimFunc))
		kafkaConsumerLabel = "alertconsumer"
	case constants.JOBTYPE_COMPOSITE_CONSUMER:
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.CompositeConsumerClaimFunc))
		kafkaConsumerLabel = "compositeconsumer"
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
	case constants.JOBTYPE_JUMBOV2_EVENTS_CONSUMER:
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ZomatoJumboV2EventConsumerClaimFunc))
		kafkaConsumerLabel = "jumbov2eventconsumer"
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
		helpers.AddProducerToSetup(constants.OFFLINE_MSK)
		helpers.AddProducerToSetup(constants.DISTRICT_TENANT)
	case constants.JOBTYPE_NUGGET_JUMBOV2_EVENTS_CONSUMER:
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.NuggetJumboV2EventConsumerClaimFunc))
		kafkaConsumerLabel = "jumbov2eventconsumer"
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
		helpers.AddProducerToSetup(constants.OFFLINE_MSK)
	case constants.JOBTYPE_DISTRICT_JUMBOV2_EVENTS_CONSUMER:
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.DistrictJumboV2EventConsumerClaimFunc))
		kafkaConsumerLabel = "districtjumbov2eventconsumer"
		helpers.AddProducerToSetup(kafka.DEFAULT_PRODUCER_NAME)
		helpers.AddProducerToSetup(constants.OFFLINE_MSK)
		helpers.AddProducerToSetup(constants.DISTRICT_TENANT)
	case constants.JOBTYPE_BLINKIT_JUMBOV2_EVENTS_CONSUMER:
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.BlinkitJumboV2EventConsumerClaimFunc))
		kafkaConsumerLabel = "blinkitjumbov2eventconsumer"
		helpers.AddProducerToSetup(constants.BLINKIT_TENANT)
		helpers.AddProducerToSetup(constants.BLINKIT_WARPSTREAM)
		helpers.AddProducerToSetup(constants.OFFLINE_MSK)
	case constants.JOBTYPE_PROTO_CONVERTOR_FRONTEND:
		handlers.InitProtoConvertorMap(ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ProtoConvertorClaimFuncFrontend))
		kafkaConsumerLabel = "protoconvertorconsumerfrontend"
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.priority-kafka"))
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.kafka"))
	case constants.JOBTYPE_PROTO_CONVERTOR_BACKEND:
		handlers.InitProtoConvertorMap(ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ProtoConvertorClaimFuncBackend))
		kafkaConsumerLabel = "protoconvertorconsumerbackend"
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.priority-kafka"))
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.kafka"))
	case constants.JOBTYPE_BLINKIT_PROTO_CONVERTOR_BACKEND:
		handlers.InitProtoConvertorMap(ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ProtoConvertorClaimFuncBackend))
		kafkaConsumerLabel = "blinkitprotoconvertorconsumerbackend"
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.priority-kafka"))
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.kafka"))
	case constants.JOBTYPE_BLINKIT_PROTO_CONVERTOR_FRONTEND:
		handlers.InitProtoConvertorMap(ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ProtoConvertorClaimFuncFrontend))
		kafkaConsumerLabel = "blinkitprotoconvertorconsumerfrontend"
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.priority-kafka"))
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.kafka"))
	case constants.JOBTYPE_DISTRICT_PROTO_CONVERTOR:
		handlers.InitProtoConvertorMap(ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ProtoConvertorClaimFuncFrontend))
		kafkaConsumerLabel = "districtprotoconvertorconsumer"
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.priority-kafka"))
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.kafka"))
	case constants.JOBTYPE_FEEDINGINDIA_PROTO_CONVERTOR:
		handlers.InitProtoConvertorMap(ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.ProtoConvertorClaimFuncFrontend))
		kafkaConsumerLabel = "feedingindiaprotoconvertorconsumer"
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.priority-kafka"))
		helpers.AddProducerToSetup(config.GetString(ctx, "proto-convertor.kafka"))
	case constants.JOBTYPE_BISTRO_CLEVERTAP_CONSUMER:
		kafkaConsumerLabel = "bistroclevertapconsumer"
		tenant := config.GetString(ctx, fmt.Sprintf("kafka.consumers.%s.destinations.clevertap", kafkaConsumerLabel))
		ctx = context.WithValue(ctx, constants.TenantCtxKey, tenant)

		clevertapClient := clevertap.NewClient(&clevertap.Config{
			AccountID: config.GetString(ctx, fmt.Sprintf("clevertap.producers.%s.accountid", tenant)),
			Passcode:  config.GetString(ctx, fmt.Sprintf("clevertap.producers.%s.passcode", tenant)),
			Endpoint:  config.GetString(ctx, fmt.Sprintf("clevertap.producers.%s.endpoint", tenant)),
		})
		environment := env.NewEnv(
			env.WithClevertapClient(clevertapClient),
		)
		ctx = environment.WithContext(ctx)
		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.CleverTapConsumerClaimFunc(ctx)))

	case constants.JOBTYPE_BLINKIT_CLEVERTAP_CONSUMER:
		kafkaConsumerLabel = "blinkitorderlifecycle"
		tenant := config.GetString(ctx, fmt.Sprintf("kafka.consumers.%s.destinations.clevertap", kafkaConsumerLabel))
		ctx = context.WithValue(ctx, constants.TenantCtxKey, tenant)

		// TODO: Think of a better way to initialize all destinations (generalize SetupProducers)
		clevertapClient := clevertap.NewClient(&clevertap.Config{
			AccountID: config.GetString(ctx, fmt.Sprintf("clevertap.producers.%s.accountid", tenant)),
			Passcode:  config.GetString(ctx, fmt.Sprintf("clevertap.producers.%s.passcode", tenant)),
			Endpoint:  config.GetString(ctx, fmt.Sprintf("clevertap.producers.%s.endpoint", tenant)),
		})
		environment := env.NewEnv(
			env.WithClevertapClient(clevertapClient),
		)
		ctx = environment.WithContext(ctx)

		kafka.SetConsumerClaimFunc(claim.ConsumerClaimWrapper(handlers.CleverTapConsumerClaimFunc(ctx)))

	default:
		log.Error("Unable to set consumer claim function")
	}

	kafka.SetupProducers(ctx, helpers.ProducersToSetup)

	kafka.Start(kafkaConsumerLabel, ctx)
}
