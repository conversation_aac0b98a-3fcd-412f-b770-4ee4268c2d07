package handlers

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/Shopify/sarama"
	"github.com/Zomato/flash-gateway/internal/constants"
	"github.com/Zomato/flash-gateway/internal/helpers"
	"github.com/Zomato/flash-gateway/internal/models"
	"github.com/Zomato/flash-gateway/pkg/services/kafka"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/annotations"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	_ "github.com/Zomato/jumbo-event-registry-client-golang/registry" // Requires registry
	"github.com/spf13/viper"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
	googlejson "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"
	"google.golang.org/protobuf/types/dynamicpb"
	googleanypb "google.golang.org/protobuf/types/known/anypb"
)

var ProtoConvertorMap map[string]*models.ProtoConvertorHelper

func InitProtoConvertorMap(ctx context.Context) {
	ProtoConvertorMap = make(map[string]*models.ProtoConvertorHelper)
	pkgPrefix := viper.GetString("job_groups.default.jumbo_streaming_tables_package_prefix")
	protoregistry.GlobalTypes.RangeMessages(func(mt protoreflect.MessageType) bool {
		if strings.HasPrefix(string(mt.Descriptor().FullName()), pkgPrefix) {
			protoConvertorHelper := models.NewProtoConvertorHelper(mt)
			if protoConvertorHelper.HasOptions() {
				fmt.Println("loading " + string(mt.Descriptor().FullName()))
				ProtoConvertorMap[protoConvertorHelper.GetTenantTable()] = protoConvertorHelper
			}
		}
		return true
	})
}

func ProcessProtoMessage(
	ctx context.Context,
	tableKey string,
	headerString string,
	payloadString string,
	tenantOverride string,
	partition []byte) error {
	var protoConvertorHelper *models.ProtoConvertorHelper

	tenantTableName := constants.TENANT_ZOMATO + tableKey
	if value, ok := ProtoConvertorMap[tenantTableName]; ok {
		protoConvertorHelper = value
	} else if value, ok := ProtoConvertorMap[constants.TENANT_HYPERPURE+tableKey]; ok {
		tenantTableName = constants.TENANT_HYPERPURE + tableKey
		protoConvertorHelper = value
	} else if value, ok := ProtoConvertorMap[constants.TENANT_BLINKIT+tableKey]; ok {
		tenantTableName = constants.TENANT_BLINKIT + tableKey
		protoConvertorHelper = value
	} else if value, ok := ProtoConvertorMap[constants.TENANT_DISTRICT+tableKey]; ok {
		tenantTableName = constants.TENANT_DISTRICT + tableKey
		protoConvertorHelper = value
	} else if value, ok := ProtoConvertorMap[constants.TENANT_NUGGET+tableKey]; ok {
		tenantTableName = constants.TENANT_NUGGET + tableKey
		protoConvertorHelper = value
	} else if value, ok := ProtoConvertorMap[constants.TENANT_FEEDINGINDIA+tableKey]; ok {
		tenantTableName = constants.TENANT_FEEDINGINDIA + tableKey
		protoConvertorHelper = value
	} else {
		return nil
	}

	headerProto, err := getHeaderProto(headerString)

	if err != nil {
		log.Error(err.Error())
	}

	valueString := gjson.Get(payloadString, "value").String()
	valueProto := *protoConvertorHelper.ProtoMessage

	customFunction, isPresent := CustomHandlingMap()[tenantTableName]
	if isPresent {
		valueString = customFunction(valueString)
	}

	if tenantTableName == "ZOMATO.jevent" {
		valueProto, err = getJeventProto(valueString)
		if err == nil && valueProto == nil {
			return nil
		}
	} else {
		valueProto = dynamicpb.NewMessage(valueProto.ProtoReflect().Descriptor())
		err = deserialiseMessage(valueProto, []byte(valueString))
	}
	if err != nil {
		valueProto = getJumboFailedEventsProto(valueString, err.Error(), tableKey)
		protoConvertorHelper = ProtoConvertorMap[constants.ERRORS_TENANT_TABLE]
		statsd.PushFailedMessagesCountTableLevel(ctx, tableKey, "deserialisation_failed")
	}

	extensions := protoConvertorHelper.Options

	AnyTable, err := googleanypb.New(valueProto)

	if err != nil {
		statsd.PushFailedMessagesCountTableLevel(ctx, tableKey, "any_table_conversion_failed")
		log.Error("Any table conversion failed, trying to convert it to jumbo failed event")
		log.Error(err.Error())
		if extensions.Table != constants.ERRORS_TABLE {
			protoConvertorHelper = ProtoConvertorMap[constants.ERRORS_TENANT_TABLE]
			extensions = protoConvertorHelper.Options
			valueProto = getJumboFailedEventsProto(valueString, err.Error(), tableKey)
			AnyTable, err = googleanypb.New(valueProto)
			if err != nil {
				log.Error("Any table conversion also failed jumbo failed event, skipping the event")
				log.Error(err.Error())
				return err
			}
		} else {
			return err
		}
	}

	priorityLevelStr := gjson.Get(payloadString, "priority_level").String()
	priorityLevel := getEventPriority(priorityLevelStr)

	sequenceId := gjson.Get(payloadString, "sequence_id").String()
	sequenceOffset := helpers.GetUInt32ValueFromJsonString(payloadString, "sequence_offset")
	sendMessage(ctx, headerProto, AnyTable, extensions, gjson.Get(payloadString, "url").String(),
		partition, tenantOverride, gjson.Get(payloadString, "event_id").String(),
		sequenceId, sequenceOffset, event.PriorityLevel_PRIORITY_LEVEL_UNSPECIFIED)

	if priorityLevel != event.PriorityLevel_PRIORITY_LEVEL_UNSPECIFIED {
		log.WithFields(map[string]interface{}{
			"priority_level_enum": priorityLevel,
			"table":               extensions.Table,
		}).Info("Sending message to priority topic")
		sendMessage(ctx, headerProto, AnyTable, extensions, gjson.Get(payloadString, "url").String(),
			partition, tenantOverride, gjson.Get(payloadString, "event_id").String(),
			sequenceId, sequenceOffset, priorityLevel)
	}

	return nil
}

func ProtoConvertorClaimFuncBackend(ctx context.Context, message *sarama.ConsumerMessage) error {

	var payloads models.AppPayloadWrapper
	var error error
	err := json.Unmarshal(message.Value, &payloads)
	if err != nil {
		return errors.New("Unable to unmarshal message. Error: " + err.Error())
	}

	for _, payload := range payloads.AppPayload {

		key := payload.Payload["key"]
		if key == "" {
			log.Warn(ctx, "Unable to get key", zap.Any("payload", payload.Payload))
			continue
		}
		headerInterface, payloadInterface := payload.Header, payload.Payload
		headerString, err := json.Marshal(headerInterface)
		if err != nil {
			log.Error("error converting header to string")
			return err
		}
		valueString, err := json.Marshal(payloadInterface)
		if err != nil {
			log.Error(err.Error())
			continue
		}
		error = protoConvertorParser(ctx, string(headerString), string(valueString), message.Topic, message.Key)
	}
	return error
}

func ProtoConvertorClaimFuncFrontend(ctx context.Context, message *sarama.ConsumerMessage) error {
	headerString, payloadString := helpers.SeparateMessageParts(string(message.Value))
	return protoConvertorParser(ctx, headerString, payloadString[0], message.Topic, message.Key)
}

func protoConvertorParser(ctx context.Context, headerString string, payloadString string, messageTopic string, messageKey []byte) error {
	tableKey := gjson.Get(payloadString, "key").String()

	if value, ok := constants.PROTO_TABLE_CONVERT[tableKey]; ok {
		tableKey = value
	}

	var tenantOverride string
	var namespace string
	if namespace = gjson.Get(headerString, "namespace").String(); strings.Contains(strings.ToLower(namespace), "nugget") {
		tenantOverride = strings.ToLower(namespace)
	} else if strings.Contains(messageTopic, "blinkit") {
		tenantOverride = "blinkit"
	} else if strings.Contains(messageTopic, "district") {
		tenantOverride = "district"
	} else if strings.Contains(messageTopic, "feedingindia") {
		tenantOverride = "feedingindia"
	}

	err := ProcessProtoMessage(ctx, tableKey, headerString, payloadString, tenantOverride, messageKey)

	if err != nil {
		return err
	}

	return nil
}

func sendMessage(ctx context.Context, headerProto *event.Header,
	AnyTable *googleanypb.Any, extensions *annotations.JumboMessageOptions,
	Url string, partition []byte, tenantOverride string, event_id string,
	sequenceId string, sequenceOffset uint32, priorityLevel event.PriorityLevel) {
	var tenant string
	if tenantOverride != "" {
		tenant = tenantOverride
	} else if extensions.GetTenant() != "" {
		tenant = strings.ToLower(extensions.GetTenant())
	} else {
		tenant = strings.ToLower(extensions.GetTenants()[0])
	}
	jumboRealtimeMessage := &event.JumboEvent{
		Table:          extensions.Table,
		Header:         headerProto,
		Payload:        AnyTable,
		Url:            Url,
		EventId:        event_id,
		SequenceId:     sequenceId,
		SequenceOffset: sequenceOffset,
		PriorityLevel:  priorityLevel,
	}

	topicName := fmt.Sprintf(`%s.%s.%s.%s`, constants.PROTO_CONVERTOR_OUTPUT_TOPIC_PREFIX, tenant, extensions.Database, extensions.Table)
	var producerType string
	if priorityLevel != event.PriorityLevel_PRIORITY_LEVEL_UNSPECIFIED {
		topicName = fmt.Sprintf(`%s.flash.priority_events`, tenant)
		producerType = "proto-convertor.priority-kafka"
	} else {
		producerType = "proto-convertor.kafka"
	}

	producer := kafka.Producers[config.GetString(ctx, producerType)]
	producer.PublishProto(ctx, strings.ToLower(topicName), jumboRealtimeMessage, partition)
}

func deserialiseMessage(protoClass protoreflect.ProtoMessage, message []byte) error {

	options := googlejson.UnmarshalOptions{DiscardUnknown: true}
	err := options.Unmarshal(message, protoClass)
	//err := googlejson.Unmarshal(message, protoClass)
	if err != nil {
		log.Error(err.Error() + " | " + string(message))
	}
	return err
}
