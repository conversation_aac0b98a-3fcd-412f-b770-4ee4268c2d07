package models

// ProductImageShownEventData represents the complete structure for product image shown events
type ProductImageShownEventData struct {
	Source             string                          `json:"source"`
	DeviceID           string                          `json:"device_id"`
	SessionID          string                          `json:"session_id"`
	UserID             string                          `json:"user_id"`
	UserAgent          string                          `json:"user_agent"`
	Timestamp          int64                           `json:"timestamp"`
	Time               int64                           `json:"time"`
	IngestionTimestamp int64                           `json:"ingestion_timestamp"`
	Location           uint32                          `json:"location"`
	AppInfo            AppInfoData                     `json:"app_info"`
	EventName          string                          `json:"event_name"`
	Traits             ProductImageShownTraitsData     `json:"traits"`
	Properties         ProductImageShownPropertiesData `json:"properties"`
	URL                string                          `json:"url"`
	EventID            string                          `json:"event_id"`
	SequenceID         string                          `json:"sequence_id"`
	SequenceOffset     int64                           `json:"sequence_offset"`
}

// ProductImageShownTraitsData represents the traits section with omitempty support
type ProductImageShownTraitsData struct {
	AppFlavor              *string  `json:"app_flavor,omitempty"`
	CartID                 *string  `json:"cart_id,omitempty"`
	ChainID                *int32   `json:"chain_id,omitempty"`
	Channel                *string  `json:"channel,omitempty"`
	CityID                 *int32   `json:"city_id,omitempty"`
	CityName               *string  `json:"city_name,omitempty"`
	DeviceUUID             *string  `json:"device_uuid,omitempty"`
	InstallCampaign        *string  `json:"install_campaign,omitempty"`
	InstallSource          *string  `json:"install_source,omitempty"`
	Latitude               *float64 `json:"latitude,omitempty"`
	LifetimeOrders         *int32   `json:"lifetime_orders,omitempty"`
	Longitude              *float64 `json:"longitude,omitempty"`
	MerchantID             *int32   `json:"merchant_id,omitempty"`
	MerchantName           *string  `json:"merchant_name,omitempty"`
	MonthlyOrders          *float64 `json:"monthly_orders,omitempty"`
	SegmentEnabledFeatures []string `json:"segment_enabled_features,omitempty"`
	SessionLaunchSource    *string  `json:"session_launch_source,omitempty"`
	SessionUUID            *string  `json:"session_uuid,omitempty"`
	TotalOrderValue        *float64 `json:"total_order_value,omitempty"`
	UserType               *string  `json:"user_type,omitempty"`
	AppVersionCode         *int64   `json:"app_version_code,omitempty"`
	UserExperimentBuckets  []string `json:"user_experiment_buckets,omitempty"`
	InstallMedium          *string  `json:"install_medium,omitempty"`
	InstallReferrer        *string  `json:"install_referrer,omitempty"`
	IsDefaultMerchant      *bool    `json:"is_default_merchant,omitempty"`
	TrackingID             *string  `json:"tracking_id,omitempty"`
	AppsflyerAppInstanceID *string  `json:"appsflyer_app_instance_id,omitempty"`
	FirebaseAppInstanceID  *string  `json:"firebase_app_instance_id,omitempty"`
	LocationHex            *string  `json:"location_hex,omitempty"`
	HostAppType            *string  `json:"host_app_type,omitempty"`
	HostAppVersion         *string  `json:"host_app_version,omitempty"`
	HostAppVersionCode     *int64   `json:"host_app_version_code,omitempty"`
	HostAppUserID          *string  `json:"host_app_user_id,omitempty"`
	SegmentType            []string `json:"segment_type,omitempty"`
}

// ProductImageShownPropertiesData represents the properties section with omitempty support
// Product represents a single product in the event
type Product struct {
	ProductID      *int64   `json:"product_id,omitempty"`
	L0Category     *string  `json:"l0_category,omitempty"`
	L1Category     *string  `json:"l1_category,omitempty"`
	L2Category     *string  `json:"l2_category,omitempty"`
	PType          *string  `json:"ptype,omitempty"`
	Name           *string  `json:"name,omitempty"`
	Brand          *string  `json:"brand,omitempty"`
	Price          *float64 `json:"price,omitempty"`
	MRP            *float64 `json:"mrp,omitempty"`
	Quantity       *int32   `json:"quantity,omitempty"`
	TypeID         *int64   `json:"type_id,omitempty"`
	Currency       *string  `json:"currency,omitempty"`
	ShipmentID     *string  `json:"shipment_id,omitempty"`
	InventoryLimit *int64   `json:"inventory_limit,omitempty"`
	SBCPrice       *float64 `json:"sbc_price,omitempty"`
}

// Shipment represents a single shipment in the event
type Shipment struct {
	ShipmentID     *string  `json:"shipment_id,omitempty"`
	ShipmentType   *string  `json:"shipment_type,omitempty"`
	SlotAmount     *float64 `json:"slot_amount,omitempty"`
	ShipmentValue  *float64 `json:"shipment_value,omitempty"`
	SlotDate       *string  `json:"slot_date,omitempty"`
	SlotTime       *string  `json:"slot_time,omitempty"`
	IsEditShipment *bool    `json:"is_edit_shipment,omitempty"`
	IsEarliestSlot *bool    `json:"is_earliest_slot,omitempty"`
}

type ProductImageShownPropertiesData struct {
	Products                    []Product  `json:"products,omitempty"`
	Shipments                   []Shipment `json:"shipments,omitempty"`
	ChildWidgetID               *string    `json:"child_widget_id,omitempty"`
	ChildWidgetName             *string    `json:"child_widget_name,omitempty"`
	ChildWidgetPosition         *int32     `json:"child_widget_position,omitempty"`
	ChildWidgetRevisionID       *string    `json:"child_widget_revision_id,omitempty"`
	ChildWidgetTitle            *string    `json:"child_widget_title,omitempty"`
	ChildWidgetTrackingID       *string    `json:"child_widget_tracking_id,omitempty"`
	ChildWidgetVariationID      *string    `json:"child_widget_variation_id,omitempty"`
	EntrySourceTitle            *string    `json:"entry_source_title,omitempty"`
	IsReactPage                 *bool      `json:"is_react_page,omitempty"`
	LastPageID                  *string    `json:"last_page_id,omitempty"`
	LastPageName                *string    `json:"last_page_name,omitempty"`
	LastPageTitle               *string    `json:"last_page_title,omitempty"`
	LastPageVisitID             *string    `json:"last_page_visit_id,omitempty"`
	LastSubPageName             *string    `json:"last_sub_page_name,omitempty"`
	LastSubPageTitle            *string    `json:"last_sub_page_title,omitempty"`
	LastSubPageVisitID          *string    `json:"last_sub_page_visit_id,omitempty"`
	PageID                      *string    `json:"page_id,omitempty"`
	PageName                    *string    `json:"page_name,omitempty"`
	PageRevisionID              *string    `json:"page_revision_id,omitempty"`
	PageTitle                   *string    `json:"page_title,omitempty"`
	PageTrackingID              *string    `json:"page_tracking_id,omitempty"`
	PageVariationID             *string    `json:"page_variation_id,omitempty"`
	PageVisitID                 *string    `json:"page_visit_id,omitempty"`
	SubPageName                 *string    `json:"sub_page_name,omitempty"`
	SubPageTitle                *string    `json:"sub_page_title,omitempty"`
	SubPageVisitID              *string    `json:"sub_page_visit_id,omitempty"`
	WidgetTrackingID            *string    `json:"widget_tracking_id,omitempty"`
	WidgetVariationID           *string    `json:"widget_variation_id,omitempty"`
	AddressCount                *int32     `json:"address_count,omitempty"`
	AddressID                   *string    `json:"address_id,omitempty"`
	AddressType                 *string    `json:"address_type,omitempty"`
	AdsAssetTypeID              *string    `json:"ads_asset_type_id,omitempty"`
	AdsCampaignID               *int32     `json:"ads_campaign_id,omitempty"`
	AdsCollectionID             *string    `json:"ads_collection_id,omitempty"`
	AdsSubcampaignID            *int32     `json:"ads_subcampaign_id,omitempty"`
	AdsType                     *string    `json:"ads_type,omitempty"`
	Amount                      *float64   `json:"amount,omitempty"`
	Badge                       *string    `json:"badge,omitempty"`
	Brand                       *string    `json:"brand,omitempty"`
	Campaign                    *string    `json:"campaign,omitempty"`
	CampaignID                  *string    `json:"campaign_id,omitempty"`
	CampaignIdentifier          *string    `json:"campaign_identifier,omitempty"`
	CardType                    *string    `json:"card_type,omitempty"`
	CartID                      *string    `json:"cart_id,omitempty"`
	CartType                    *string    `json:"cart_type,omitempty"`
	CartValue                   *float64   `json:"cart_value,omitempty"`
	CollectionID                *string    `json:"collection_id,omitempty"`
	CTAType                     *string    `json:"cta_type,omitempty"`
	CumulativeDelay             *float64   `json:"cumulative_delay,omitempty"`
	Currency                    *string    `json:"currency,omitempty"`
	CurrentCartSavings          *float64   `json:"current_cart_savings,omitempty"`
	CustomData                  *string    `json:"custom_data,omitempty"`
	Deeplink                    *string    `json:"deeplink,omitempty"`
	DeviceLat                   *float64   `json:"device_lat,omitempty"`
	DeviceLon                   *float64   `json:"device_lon,omitempty"`
	Enabled                     *bool      `json:"enabled,omitempty"`
	EventSourceIdentifier       *string    `json:"event_source_identifier,omitempty"`
	FavouriteIconState          *string    `json:"favourite_icon_state,omitempty"`
	FewLeftBadge                *string    `json:"few_left_badge,omitempty"`
	FilterKeys                  *string    `json:"filter_keys,omitempty"`
	FiltersPresent              *string    `json:"filters_present,omitempty"`
	ID                          *string    `json:"id,omitempty"`
	ImageURL                    *string    `json:"image_url,omitempty"`
	ImagesShown                 *bool      `json:"images_shown,omitempty"`
	ImagesShownFlags            *string    `json:"images_shown_flags,omitempty"`
	InvalidIds                  []string   `json:"invalid_ids,omitempty"`
	Inventory                   *int32     `json:"inventory,omitempty"`
	InventoryLimit              *int32     `json:"inventory_limit,omitempty"`
	IsOffer                     *bool      `json:"is_offer,omitempty"`
	ItemsInCart                 *int32     `json:"items_in_cart,omitempty"`
	L0Category                  *string    `json:"l0_category,omitempty"`
	L1Category                  *string    `json:"l1_category,omitempty"`
	L2Category                  *string    `json:"l2_category,omitempty"`
	Label                       *string    `json:"label,omitempty"`
	Latitude                    *float64   `json:"latitude,omitempty"`
	Longitude                   *float64   `json:"longitude,omitempty"`
	MerchantID                  *int32     `json:"merchant_id,omitempty"`
	MerchantType                *string    `json:"merchant_type,omitempty"`
	Message                     *string    `json:"message,omitempty"`
	MRP                         *int32     `json:"mrp,omitempty"`
	Name                        *string    `json:"name,omitempty"`
	Offer                       *string    `json:"offer,omitempty"`
	OfferText                   *bool      `json:"offer_text,omitempty"`
	OrderID                     *string    `json:"order_id,omitempty"`
	OrderState                  *string    `json:"order_state,omitempty"`
	OverlayBadges               *string    `json:"overlay_badges,omitempty"`
	PageType                    *string    `json:"page_type,omitempty"`
	ParentProduct               *string    `json:"parent_product,omitempty"`
	PaymentMode                 *string    `json:"payment_mode,omitempty"`
	Position                    *int32     `json:"position,omitempty"`
	Price                       *float64   `json:"price,omitempty"`
	ProductCount                *int32     `json:"product_count,omitempty"`
	ProductID                   *int64     `json:"product_id,omitempty"`
	ProductIds                  *string    `json:"product_ids,omitempty"`
	ProductListID               *string    `json:"product_list_id,omitempty"`
	ProductOffers               *string    `json:"product_offers,omitempty"`
	ProductPosition             *int32     `json:"product_position,omitempty"`
	PromoIdentifers             []string   `json:"promo_identifers,omitempty"`
	PType                       *string    `json:"ptype,omitempty"`
	Quantity                    *int32     `json:"quantity,omitempty"`
	Rating                      *string    `json:"rating,omitempty"`
	Reason                      *string    `json:"reason,omitempty"`
	RecommendationID            *string    `json:"recommendation_id,omitempty"`
	SBCPrice                    *int32     `json:"sbc_price,omitempty"`
	SearchActualKeyword         *string    `json:"search_actual_keyword,omitempty"`
	SearchInputKeyword          *string    `json:"search_input_keyword,omitempty"`
	SearchKeywordParent         *string    `json:"search_keyword_parent,omitempty"`
	SearchKeywordType           *string    `json:"search_keyword_type,omitempty"`
	SearchPreviousKeyword       *string    `json:"search_previous_keyword,omitempty"`
	SearchResultCount           *int32     `json:"search_result_count,omitempty"`
	SearchUserTypedInputKeyword *string    `json:"search_user_typed_input_keyword,omitempty"`
	SelectedTab                 *string    `json:"selected_tab,omitempty"`
	SelectedTabPosition         *int32     `json:"selected_tab_position,omitempty"`
	SharedID                    *string    `json:"shared_id,omitempty"`
	Shipping                    *float64   `json:"shipping,omitempty"`
	Source                      *string    `json:"source,omitempty"`
	State                       *string    `json:"state,omitempty"`
	SubPageID                   *string    `json:"sub_page_id,omitempty"`
	SubPageRevisionID           *string    `json:"sub_page_revision_id,omitempty"`
	SubcategoryID               *string    `json:"subcategory_id,omitempty"`
	Subtitle                    *string    `json:"subtitle,omitempty"`
	SuggestedKeywords           *string    `json:"suggested_keywords,omitempty"`
	TimeToDeliveryInMins        *float64   `json:"time_to_delivery_in_mins,omitempty"`
	Title                       *string    `json:"title,omitempty"`
	TotalProductsInCart         *int32     `json:"total_products_in_cart,omitempty"`
	TotalProductsInSharedCart   *int32     `json:"total_products_in_shared_cart,omitempty"`
	TotalShipmentsInCart        *int32     `json:"total_shipments_in_cart,omitempty"`
	Type                        *string    `json:"type,omitempty"`
	UniqueProductsInCart        *int32     `json:"unique_products_in_cart,omitempty"`
	URL                         *string    `json:"url,omitempty"`
	WidgetID                    *string    `json:"widget_id,omitempty"`
	WidgetImpressionCount       *int32     `json:"widget_impression_count,omitempty"`
	WidgetName                  *string    `json:"widget_name,omitempty"`
	WidgetPosition              *int32     `json:"widget_position,omitempty"`
	WidgetRevisionID            *string    `json:"widget_revision_id,omitempty"`
	WidgetTitle                 *string    `json:"widget_title,omitempty"`
	AdsCostID                   *int32     `json:"ads_cost_id,omitempty"`
	ProductCTAType              *string    `json:"product_cta_type,omitempty"`
	SectionTrackingID           *string    `json:"section_tracking_id,omitempty"`
	NextAvailableAt             *string    `json:"next_available_at,omitempty"`
	ProductState                *string    `json:"product_state,omitempty"`
	HighlightIds                *string    `json:"highlight_ids,omitempty"`
	WidgetGroupTrackingID       *string    `json:"widget_group_tracking_id,omitempty"`
	EntrySourceGroupTrackingID  *string    `json:"entry_source_group_tracking_id,omitempty"`
	VideoShown                  *bool      `json:"video_shown,omitempty"`
	ETAIdentifier               *string    `json:"eta_identifier,omitempty"`
	WishlistAdded               *bool      `json:"wishlist_added,omitempty"`
	IsTopRightIconSelected      *bool      `json:"is_top_right_icon_selected,omitempty"`
	IconType                    *string    `json:"icon_type,omitempty"`
}
