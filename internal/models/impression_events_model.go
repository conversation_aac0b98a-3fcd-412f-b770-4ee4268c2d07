package models

// ImpressionEventData represents the complete structure for impression events
type ImpressionEventData struct {
	Source             string                   `json:"source"`
	DeviceID           string                   `json:"device_id"`
	SessionID          string                   `json:"session_id"`
	UserID             string                   `json:"user_id"`
	UserAgent          string                   `json:"user_agent"`
	Timestamp          int64                    `json:"timestamp"`
	Time               int64                    `json:"time"`
	IngestionTimestamp int64                    `json:"ingestion_timestamp"`
	Location           uint32                   `json:"location"`
	AppInfo            AppInfoData              `json:"app_info"`
	EventName          string                   `json:"event_name"`
	Traits             ImpressionTraitsData     `json:"traits"`
	Properties         ImpressionPropertiesData `json:"properties"`
	URL                string                   `json:"url"`
	EventID            string                   `json:"event_id"`
	SequenceID         string                   `json:"sequence_id"`
	SequenceOffset     int64                    `json:"sequence_offset"`
}

// ImpressionTraitsData represents the traits section with omitempty support
type ImpressionTraitsData struct {
	AppFlavor              *string  `json:"app_flavor,omitempty"`
	CartID                 *string  `json:"cart_id,omitempty"`
	ChainID                *int32   `json:"chain_id,omitempty"`
	Channel                *string  `json:"channel,omitempty"`
	CityID                 *int32   `json:"city_id,omitempty"`
	CityName               *string  `json:"city_name,omitempty"`
	DeviceUUID             *string  `json:"device_uuid,omitempty"`
	InstallCampaign        *string  `json:"install_campaign,omitempty"`
	InstallSource          *string  `json:"install_source,omitempty"`
	Latitude               *float64 `json:"latitude,omitempty"`
	LifetimeOrders         *int32   `json:"lifetime_orders,omitempty"`
	Longitude              *float64 `json:"longitude,omitempty"`
	MerchantID             *int32   `json:"merchant_id,omitempty"`
	MerchantName           *string  `json:"merchant_name,omitempty"`
	MonthlyOrders          *float64 `json:"monthly_orders,omitempty"`
	SegmentEnabledFeatures []string `json:"segment_enabled_features,omitempty"`
	SessionLaunchSource    *string  `json:"session_launch_source,omitempty"`
	SessionUUID            *string  `json:"session_uuid,omitempty"`
	TotalOrderValue        *float64 `json:"total_order_value,omitempty"`
	UserType               *string  `json:"user_type,omitempty"`
	AppVersionCode         *int64   `json:"app_version_code,omitempty"`
	UserExperimentBuckets  []string `json:"user_experiment_buckets,omitempty"`
	InstallMedium          *string  `json:"install_medium,omitempty"`
	InstallReferrer        *string  `json:"install_referrer,omitempty"`
	IsDefaultMerchant      *bool    `json:"is_default_merchant,omitempty"`
	TrackingID             *string  `json:"tracking_id,omitempty"`
	AppsflyerAppInstanceID *string  `json:"appsflyer_app_instance_id,omitempty"`
	LocationHex            *string  `json:"location_hex,omitempty"`
	HostAppType            *string  `json:"host_app_type,omitempty"`
	HostAppVersion         *string  `json:"host_app_version,omitempty"`
	HostAppVersionCode     *int64   `json:"host_app_version_code,omitempty"`
	HostAppUserID          *string  `json:"host_app_user_id,omitempty"`
	SegmentType            []string `json:"segment_type,omitempty"`
}

// ImpressionPropertiesData represents the properties section with omitempty support
type ImpressionPropertiesData struct {
	Products                      []ImpressionEventProduct   `json:"products,omitempty"`
	Shipments                     []ImpressionEventShipments `json:"shipments,omitempty"`
	ChildWidgetID                 *string                    `json:"child_widget_id,omitempty"`
	ChildWidgetName               *string                    `json:"child_widget_name,omitempty"`
	ChildWidgetPosition           *int32                     `json:"child_widget_position,omitempty"`
	ChildWidgetRevisionID         *string                    `json:"child_widget_revision_id,omitempty"`
	ChildWidgetTitle              *string                    `json:"child_widget_title,omitempty"`
	ChildWidgetTrackingID         *string                    `json:"child_widget_tracking_id,omitempty"`
	EntrySourceChildID            *string                    `json:"entry_source_child_id,omitempty"`
	EntrySourceChildName          *string                    `json:"entry_source_child_name,omitempty"`
	EntrySourceChildPosition      *int32                     `json:"entry_source_child_position,omitempty"`
	EntrySourceChildTitle         *string                    `json:"entry_source_child_title,omitempty"`
	EntrySourceChildTrackingID    *string                    `json:"entry_source_child_tracking_id,omitempty"`
	EntrySourceChildVariationID   *string                    `json:"entry_source_child_variation_id,omitempty"`
	EntrySourceID                 *string                    `json:"entry_source_id,omitempty"`
	EntrySourceName               *string                    `json:"entry_source_name,omitempty"`
	EntrySourcePosition           *string                    `json:"entry_source_position,omitempty"`
	EntrySourceRevisionID         *string                    `json:"entry_source_revision_id,omitempty"`
	EntrySourceTitle              *string                    `json:"entry_source_title,omitempty"`
	EntrySourceTrackingID         *string                    `json:"entry_source_tracking_id,omitempty"`
	EntrySourceVariationID        *string                    `json:"entry_source_variation_id,omitempty"`
	IsReactPage                   *bool                      `json:"is_react_page,omitempty"`
	LastPageID                    *string                    `json:"last_page_id,omitempty"`
	LastPageName                  *string                    `json:"last_page_name,omitempty"`
	LastPageRevisionID            *string                    `json:"last_page_revision_id,omitempty"`
	LastPageTitle                 *string                    `json:"last_page_title,omitempty"`
	LastPageTrackingID            *string                    `json:"last_page_tracking_id,omitempty"`
	LastPageVariationID           *string                    `json:"last_page_variation_id,omitempty"`
	LastPageVisitID               *string                    `json:"last_page_visit_id,omitempty"`
	LastSubPageName               *string                    `json:"last_sub_page_name,omitempty"`
	LastSubPageTitle              *string                    `json:"last_sub_page_title,omitempty"`
	LastSubPageVisitID            *string                    `json:"last_sub_page_visit_id,omitempty"`
	PageID                        *string                    `json:"page_id,omitempty"`
	PageName                      *string                    `json:"page_name,omitempty"`
	PageRevisionID                *string                    `json:"page_revision_id,omitempty"`
	PageTitle                     *string                    `json:"page_title,omitempty"`
	PageTrackingID                *string                    `json:"page_tracking_id,omitempty"`
	PageVariationID               *string                    `json:"page_variation_id,omitempty"`
	PageVisitID                   *string                    `json:"page_visit_id,omitempty"`
	SubPageName                   *string                    `json:"sub_page_name,omitempty"`
	SubPageTitle                  *string                    `json:"sub_page_title,omitempty"`
	SubPageVisitID                *string                    `json:"sub_page_visit_id,omitempty"`
	WidgetTrackingID              *string                    `json:"widget_tracking_id,omitempty"`
	WidgetVariationID             *string                    `json:"widget_variation_id,omitempty"`
	AddressCount                  *int32                     `json:"address_count,omitempty"`
	AddressID                     *string                    `json:"address_id,omitempty"`
	AddressType                   *string                    `json:"address_type,omitempty"`
	AdsAssetTypeID                *string                    `json:"ads_asset_type_id,omitempty"`
	AdsCampaignID                 *int32                     `json:"ads_campaign_id,omitempty"`
	AdsCollectionID               *string                    `json:"ads_collection_id,omitempty"`
	AdsSubcampaignID              *int32                     `json:"ads_subcampaign_id,omitempty"`
	AdsType                       *string                    `json:"ads_type,omitempty"`
	AerobarID                     *string                    `json:"aerobar_id,omitempty"`
	Amount                        *float64                   `json:"amount,omitempty"`
	AppInstalled                  *string                    `json:"app_installed,omitempty"`
	AvailableUpdateVersion        *string                    `json:"available_update_version,omitempty"`
	Badge                         *string                    `json:"badge,omitempty"`
	Brand                         *string                    `json:"brand,omitempty"`
	ButtonText                    *string                    `json:"button_text,omitempty"`
	Campaign                      *string                    `json:"campaign,omitempty"`
	CampaignID                    *string                    `json:"campaign_id,omitempty"`
	CampaignIdentifier            *string                    `json:"campaign_identifier,omitempty"`
	CardType                      *string                    `json:"card_type,omitempty"`
	CartID                        *string                    `json:"cart_id,omitempty"`
	CartSavings                   *float64                   `json:"cart_savings,omitempty"`
	CartType                      *string                    `json:"cart_type,omitempty"`
	CartValue                     *float64                   `json:"cart_value,omitempty"`
	CheckboxState                 *bool                      `json:"checkbox_state,omitempty"`
	ChildWidgetImpressionCount    *int32                     `json:"child_widget_impression_count,omitempty"`
	ClickSource                   *string                    `json:"click_source,omitempty"`
	CollectionID                  *string                    `json:"collection_id,omitempty"`
	CouponAmount                  *float64                   `json:"coupon_amount,omitempty"`
	CouponCode                    *string                    `json:"coupon_code,omitempty"`
	CouponDiscountValue           *float64                   `json:"coupon_discount_value,omitempty"`
	CouponType                    *string                    `json:"coupon_type,omitempty"`
	CTAType                       *string                    `json:"cta_type,omitempty"`
	CumulativeDelay               *float64                   `json:"cumulative_delay,omitempty"`
	Currency                      *string                    `json:"currency,omitempty"`
	CurrentCartSavings            *float64                   `json:"current_cart_savings,omitempty"`
	CurrentObjectID               *string                    `json:"current_object_id,omitempty"`
	CustomData                    *string                    `json:"custom_data,omitempty"`
	Data1                         *string                    `json:"data1,omitempty"`
	Data2                         *string                    `json:"data2,omitempty"`
	Data3                         *string                    `json:"data3,omitempty"`
	Data4                         *string                    `json:"data4,omitempty"`
	Deeplink                      *string                    `json:"deeplink,omitempty"`
	DeviceLat                     *float64                   `json:"device_lat,omitempty"`
	DeviceLon                     *float64                   `json:"device_lon,omitempty"`
	DialogType                    *string                    `json:"dialog_type,omitempty"`
	Enabled                       *bool                      `json:"enabled,omitempty"`
	ErrorDescription              *string                    `json:"error_description,omitempty"`
	EventSourceIdentifier         *string                    `json:"event_source_identifier,omitempty"`
	FavouriteIconState            *string                    `json:"favourite_icon_state,omitempty"`
	FilterKeys                    *string                    `json:"filter_keys,omitempty"`
	FilterPosition                *string                    `json:"filter_position,omitempty"`
	FiltersPresent                *string                    `json:"filters_present,omitempty"`
	Icon                          *string                    `json:"icon,omitempty"`
	ID                            *string                    `json:"id,omitempty"`
	ImageURL                      *string                    `json:"image_url,omitempty"`
	ImagesShown                   *bool                      `json:"images_shown,omitempty"`
	ImagesShownFlags              *string                    `json:"images_shown_flags,omitempty"`
	InvalidIds                    []string                   `json:"invalid_ids,omitempty"`
	Inventory                     *int32                     `json:"inventory,omitempty"`
	InventoryLimit                *int32                     `json:"inventory_limit,omitempty"`
	IsChecked                     *bool                      `json:"is_checked,omitempty"`
	IsCouponApplicable            *string                    `json:"is_coupon_applicable,omitempty"`
	IsEarliestSlot                *bool                      `json:"is_earliest_slot,omitempty"`
	ItemsInCart                   *int32                     `json:"items_in_cart,omitempty"`
	L0Category                    *string                    `json:"l0_category,omitempty"`
	L1Category                    *string                    `json:"l1_category,omitempty"`
	L2Category                    *string                    `json:"l2_category,omitempty"`
	Label                         *string                    `json:"label,omitempty"`
	Latitude                      *float64                   `json:"latitude,omitempty"`
	LocationFetchAccuracy         *string                    `json:"location_fetch_accuracy,omitempty"`
	LocationFetchAltitude         *string                    `json:"location_fetch_altitude,omitempty"`
	LocationUpdateType            *string                    `json:"location_update_type,omitempty"`
	Longitude                     *float64                   `json:"longitude,omitempty"`
	MerchantID                    *int32                     `json:"merchant_id,omitempty"`
	MerchantType                  *string                    `json:"merchant_type,omitempty"`
	Message                       *string                    `json:"message,omitempty"`
	MRP                           *int32                     `json:"mrp,omitempty"`
	Name                          *string                    `json:"name,omitempty"`
	NewLocality                   *string                    `json:"new_locality,omitempty"`
	NextObjectID                  *string                    `json:"next_object_id,omitempty"`
	NotificationType              *string                    `json:"notification_type,omitempty"`
	NumberOfFiles                 *int32                     `json:"number_of_files,omitempty"`
	NumberOfProducts              *int32                     `json:"number_of_products,omitempty"`
	Offer                         *string                    `json:"offer,omitempty"`
	OfferText                     *bool                      `json:"offer_text,omitempty"`
	OldLocality                   *string                    `json:"old_locality,omitempty"`
	OosPidList                    []string                   `json:"oos_pid_list,omitempty"`
	OrderCount                    *int32                     `json:"order_count,omitempty"`
	OrderHashID                   *string                    `json:"order_hash_id,omitempty"`
	OrderID                       *string                    `json:"order_id,omitempty"`
	OrderState                    *string                    `json:"order_state,omitempty"`
	OrderStatus                   *string                    `json:"order_status,omitempty"`
	OverlayBadges                 *string                    `json:"overlay_badges,omitempty"`
	OverlayPresent                *bool                      `json:"overlay_present,omitempty"`
	PageType                      *string                    `json:"page_type,omitempty"`
	Payload                       *string                    `json:"payload,omitempty"`
	PaymentMethod                 *string                    `json:"payment_method,omitempty"`
	PaymentMode                   *string                    `json:"payment_mode,omitempty"`
	PopupType                     *string                    `json:"popup_type,omitempty"`
	Position                      *int32                     `json:"position,omitempty"`
	PostChangeLatitude            *string                    `json:"post_change_latitude,omitempty"`
	PostChangeLongitude           *string                    `json:"post_change_longitude,omitempty"`
	PrevLabel                     *string                    `json:"prev_label,omitempty"`
	Price                         *float64                   `json:"price,omitempty"`
	ProductAlternativesList       []ProductAlternatives      `json:"product_alternatives_list,omitempty"`
	ProductCount                  *int32                     `json:"product_count,omitempty"`
	ProductID                     *int64                     `json:"product_id,omitempty"`
	ProductIds                    *string                    `json:"product_ids,omitempty"`
	ProductListID                 *string                    `json:"product_list_id,omitempty"`
	ProductPosition               *int32                     `json:"product_position,omitempty"`
	ProductsInShipment            []ProductsInShipment       `json:"products_in_shipment,omitempty"`
	PromoIdentifers               []string                   `json:"promo_identifers,omitempty"`
	PType                         *string                    `json:"ptype,omitempty"`
	Quantity                      *int32                     `json:"quantity,omitempty"`
	Rating                        *string                    `json:"rating,omitempty"`
	Reason                        *string                    `json:"reason,omitempty"`
	RecommendationID              *string                    `json:"recommendation_id,omitempty"`
	SBCCartSavings                *float64                   `json:"sbc_cart_savings,omitempty"`
	SdkVersion                    *string                    `json:"sdk_version,omitempty"`
	SearchActualKeyword           *string                    `json:"search_actual_keyword,omitempty"`
	SearchInputKeyword            *string                    `json:"search_input_keyword,omitempty"`
	SearchKeywordParent           *string                    `json:"search_keyword_parent,omitempty"`
	SearchKeywordType             *string                    `json:"search_keyword_type,omitempty"`
	SearchPreviousKeyword         *string                    `json:"search_previous_keyword,omitempty"`
	SearchResultCount             *int32                     `json:"search_result_count,omitempty"`
	SearchUserTypedInputKeyword   *string                    `json:"search_user_typed_input_keyword,omitempty"`
	Selected                      *bool                      `json:"selected,omitempty"`
	SelectedItems                 *string                    `json:"selected_items,omitempty"`
	SelectedTab                   *string                    `json:"selected_tab,omitempty"`
	ServiceType                   *string                    `json:"service_type,omitempty"`
	SharedID                      *string                    `json:"shared_id,omitempty"`
	ShipmentID                    *string                    `json:"shipment_id,omitempty"`
	ShipmentType                  *string                    `json:"shipment_type,omitempty"`
	ShipmentValue                 *float64                   `json:"shipment_value,omitempty"`
	Shipping                      *float64                   `json:"shipping,omitempty"`
	SlotAmount                    *float64                   `json:"slot_amount,omitempty"`
	SlotDate                      *string                    `json:"slot_date,omitempty"`
	SlotTime                      *string                    `json:"slot_time,omitempty"`
	Source                        *string                    `json:"source,omitempty"`
	SSID                          *string                    `json:"ssid,omitempty"`
	State                         *string                    `json:"state,omitempty"`
	Status                        *string                    `json:"status,omitempty"`
	SubPageID                     *string                    `json:"sub_page_id,omitempty"`
	SubPageRevisionID             *string                    `json:"sub_page_revision_id,omitempty"`
	SubcategoryID                 *string                    `json:"subcategory_id,omitempty"`
	Subtitle                      *string                    `json:"subtitle,omitempty"`
	SubtitleTag                   *string                    `json:"subtitle_tag,omitempty"`
	SuggestedKeywords             *string                    `json:"suggested_keywords,omitempty"`
	SuggestionPosition            *string                    `json:"suggestion_position,omitempty"`
	SuggestionSource              *string                    `json:"suggestion_source,omitempty"`
	SuggestionType                *string                    `json:"suggestion_type,omitempty"`
	SuggestionValue               *string                    `json:"suggestion_value,omitempty"`
	SwipeDirection                *string                    `json:"swipe_direction,omitempty"`
	TimeToDeliveryInMins          *float64                   `json:"time_to_delivery_in_mins,omitempty"`
	TipAmount                     *float64                   `json:"tip_amount,omitempty"`
	Title                         *string                    `json:"title,omitempty"`
	Total                         *float64                   `json:"total,omitempty"`
	TotalItems                    *int32                     `json:"total_items,omitempty"`
	TotalProductsInCart           *int32                     `json:"total_products_in_cart,omitempty"`
	TotalProductsInSharedCart     *int32                     `json:"total_products_in_shared_cart,omitempty"`
	TotalProductsInShipment       *int32                     `json:"total_products_in_shipment,omitempty"`
	TotalProductsOutOfStock       *int32                     `json:"total_products_out_of_stock,omitempty"`
	TotalShipmentsInCart          *int32                     `json:"total_shipments_in_cart,omitempty"`
	Type                          *string                    `json:"type,omitempty"`
	UniqueProducts                *int32                     `json:"unique_products,omitempty"`
	UniqueProductsInCart          *int32                     `json:"unique_products_in_cart,omitempty"`
	UniqueProductsInShipment      *int32                     `json:"unique_products_in_shipment,omitempty"`
	UniqueProductsOutOfStock      *int32                     `json:"unique_products_out_of_stock,omitempty"`
	URL                           *string                    `json:"url,omitempty"`
	WidgetID                      *string                    `json:"widget_id,omitempty"`
	WidgetImpressionCount         *int32                     `json:"widget_impression_count,omitempty"`
	WidgetName                    *string                    `json:"widget_name,omitempty"`
	WidgetPosition                *int32                     `json:"widget_position,omitempty"`
	WidgetRevisionID              *string                    `json:"widget_revision_id,omitempty"`
	WidgetTitle                   *string                    `json:"widget_title,omitempty"`
	WidgetType                    *int32                     `json:"widget_type,omitempty"`
	AdsCostID                     *int32                     `json:"ads_cost_id,omitempty"`
	Merchants                     []Merchant                 `json:"merchants,omitempty"`
	OOSProducts                   []OOSProduct               `json:"oos_products,omitempty"`
	OfferCode                     *string                    `json:"offer_code,omitempty"`
	SectionTrackingID             *string                    `json:"section_tracking_id,omitempty"`
	NextAvailableAt               *string                    `json:"next_available_at,omitempty"`
	IsDonationGiven               *bool                      `json:"is_donation_given,omitempty"`
	ProductState                  *string                    `json:"product_state,omitempty"`
	LastSubPageID                 *string                    `json:"last_sub_page_id,omitempty"`
	WatchedDuration               *float64                   `json:"watched_duration,omitempty"`
	Duration                      *float64                   `json:"duration,omitempty"`
	IsSurpriseOrder               *bool                      `json:"is_surprise_order,omitempty"`
	IsItemsHidden                 *bool                      `json:"is_items_hidden,omitempty"`
	HighlightIDs                  *string                    `json:"highlight_ids,omitempty"`
	WidgetGroupTrackingID         *string                    `json:"widget_group_tracking_id,omitempty"`
	EntrySourceGroupTrackingID    *string                    `json:"entry_source_group_tracking_id,omitempty"`
	VideoShown                    *bool                      `json:"video_shown,omitempty"`
	IsOtpDetected                 *bool                      `json:"is_otp_detected,omitempty"`
	InputType                     *InputType                 `json:"input_type,omitempty"`
	BirthDate                     *string                    `json:"birth_date,omitempty"`
	Preference                    *string                    `json:"preference,omitempty"`
	IsBottomSheetCurrentlyShown   *bool                      `json:"is_bottom_sheet_currently_shown,omitempty"`
	HasBottomSheetBeenShownBefore *bool                      `json:"has_bottom_sheet_been_shown_before,omitempty"`
	IsAddressSelectedManually     *bool                      `json:"is_address_selected_manually,omitempty"`
	Distance                      *int32                     `json:"distance,omitempty"`
	CouponID                      *string                    `json:"coupon_id,omitempty"`
	ErrorType                     *string                    `json:"error_type,omitempty"`
	ErrorMessage                  *string                    `json:"error_message,omitempty"`
	EtaIdentifier                 *string                    `json:"eta_identifier,omitempty"`
	WishlistAdded                 *bool                      `json:"wishlist_added,omitempty"`
	LoginMethod                   *string                    `json:"login_method,omitempty"`
	IsLowPowerMode                *bool                      `json:"is_low_power_mode,omitempty"`
	IsAccessibilityEnabled        *bool                      `json:"is_accessibility_enabled,omitempty"`
}

// ProductsInShipment represents a product in a shipment
type ProductsInShipment struct {
	ProductID *int64 `json:"product_id,omitempty"`
}

// Product represents a product in the impression events
type ImpressionEventProduct struct {
	ProductID      *int64   `json:"product_id,omitempty"`
	L0Category     *string  `json:"l0_category,omitempty"`
	L1Category     *string  `json:"l1_category,omitempty"`
	L2Category     *string  `json:"l2_category,omitempty"`
	PType          *string  `json:"ptype,omitempty"`
	Name           *string  `json:"name,omitempty"`
	Brand          *string  `json:"brand,omitempty"`
	Price          *float64 `json:"price,omitempty"`
	MRP            *float64 `json:"mrp,omitempty"`
	Quantity       *int32   `json:"quantity,omitempty"`
	TypeID         *int64   `json:"type_id,omitempty"`
	Currency       *string  `json:"currency,omitempty"`
	ShipmentID     *string  `json:"shipment_id,omitempty"`
	InventoryLimit *int64   `json:"inventory_limit,omitempty"`
	SBCPrice       *float64 `json:"sbc_price,omitempty"`
}

// Shipment represents a shipment in the impression events
type ImpressionEventShipments struct {
	ShipmentID     *string  `json:"shipment_id,omitempty"`
	ShipmentType   *string  `json:"shipment_type,omitempty"`
	SlotAmount     *float64 `json:"slot_amount,omitempty"`
	ShipmentValue  *float64 `json:"shipment_value,omitempty"`
	SlotDate       *string  `json:"slot_date,omitempty"`
	SlotTime       *string  `json:"slot_time,omitempty"`
	IsEditShipment *bool    `json:"is_edit_shipment,omitempty"`
	IsEarliestSlot *bool    `json:"is_earliest_slot,omitempty"`
}

// ProductAlternatives represents product alternatives data
type ProductAlternatives struct {
	PID          *string  `json:"pid,omitempty"`
	Alternatives []string `json:"alternatives,omitempty"`
}

// Merchant represents merchant information in the impression events
type Merchant struct {
	IsServiceable        *bool   `json:"is_serviceable,omitempty"`
	MerchantID           *int32  `json:"merchant_id,omitempty"`
	ServiceabilityReason *string `json:"serviceability_reason,omitempty"`
	ProductIDs           []int64 `json:"product_ids,omitempty"`
	AssortmentTag        *string `json:"assortment_tag,omitempty"`
}

// OOSProduct represents an out-of-stock product
type OOSProduct struct {
	Reason *string `json:"reason,omitempty"`
	ID     *int64  `json:"id,omitempty"`
}

// InputType represents the input type for phone number field
type InputType string

const (
	INPUT_TYPE_UNSPECIFIED InputType = "INPUT_TYPE_UNSPECIFIED"
	INPUT_TYPE_AUTO        InputType = "AUTO"
	INPUT_TYPE_MANUAL      InputType = "MANUAL"
)
