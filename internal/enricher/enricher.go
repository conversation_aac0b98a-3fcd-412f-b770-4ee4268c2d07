package enricher

import (
	"context"
	"errors"
	"regexp"
	"strings"
	"time"

	"github.com/Shopify/sarama"
	"github.com/Zomato/flash-gateway/internal/helpers"
	"github.com/Zomato/flash-gateway/pkg/services/cache"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary

	androidSourceAppVersionRegex = regexp.MustCompile(`app_version=v((\d+\.?)+)`)
	iosSourceAppVersionRegex     = regexp.MustCompile(`consumer((\d+\.?)+)`)
	appSourceAppVersionRegex     = regexp.MustCompile(`Zomato/((\d+\.?)+)`)
)

func enrich(ctx context.Context, message string, enricherDB helpers.EnrichTopicConf) (string, error) {
	var messageMap map[string]interface{}
	err := json.Unmarshal([]byte(message), &messageMap)
	if err != nil {
		return "", errors.New("Unable to unamrshal messasge. Error: " + err.Error())
	}

	// Push event lag
	if timestampInterface, ok := messageMap[enricherDB.EventTimeColumn]; ok {
		timestamp, ok := timestampInterface.(string)
		if ok {
			emissionTime, err := time.Parse(enricherDB.EventTimeFormat, timestamp)
			if err == nil {
				statsd.PushEventLag(helpers.ComputeEventLag(emissionTime), ctx)
			}
		}
	}

	cacheValues := make(map[string]map[string]interface{})
	for _, enrichField := range enricherDB.Enrichment {
		if enrichField.FieldType != helpers.Cache {
			continue
		}

		cacheKey := enrichField.LookupKey + messageMap[enrichField.BaseField].(string)
		if value, ok := cacheValues[cacheKey]; ok {
			value[enrichField.LookupField] = nil
			cacheValues[cacheKey] = value
		} else {
			cacheValues[cacheKey] = map[string]interface{}{
				enrichField.LookupField: nil,
			}
		}
	}

	cache.SetL2CacheKeyFunc(func(keys ...string) string {
		return strings.Join(keys, ":")
	})
	cache.Get(ctx, &cacheValues)

	for _, enrichField := range enricherDB.Enrichment {
		if enrichField.FieldType == helpers.Derived {
			if enrichField.EnrichedField == "app_version" {
				messageMap[enrichField.EnrichedField] = extractAppVersionFromPayload(messageMap)
			}

			continue
		}

		if enrichField.FieldType == helpers.Now {
			messageMap[enrichField.EnrichedField] = time.Now().Format("2006-06-02T15:04:05Z")
			continue
		}

		if enrichField.FieldType == helpers.Cache {
			if _, ok := messageMap[enrichField.BaseField]; ok {
				messageMap[enrichField.EnrichedField] = cacheValues[enrichField.LookupKey+messageMap[enrichField.BaseField].(string)][enrichField.LookupField]
			}
		}
	}

	enrichedMesssage, err := json.Marshal(messageMap)
	if err != nil {
		return "", errors.New("Unable to build enriched message. Error: " + err.Error())
	}

	return string(enrichedMesssage), nil
}

func extractAppVersionFromPayload(payload map[string]interface{}) string {
	userAgentInterface, ok := payload["user_agent"]
	if !ok {
		return ""
	}

	userAgent, ok := userAgentInterface.(string)
	if !ok {
		return ""
	}

	source := payload["source"].(string)

	switch source {
	case "app", "android":
		if strings.HasPrefix(userAgent, "&") {
			version := androidSourceAppVersionRegex.FindStringSubmatch(userAgent)
			if version == nil {
				return ""
			}

			return version[1]
		}

		version := appSourceAppVersionRegex.FindStringSubmatch(userAgent)
		if version == nil {
			return ""
		}

		return version[1]
	case "ios":
		version := iosSourceAppVersionRegex.FindStringSubmatch(userAgent)
		if version == nil {
			return ""
		}

		return version[1]
	}

	return ""
}

// GetEnrichedMessage enriches the message
func GetEnrichedMessage(ctx context.Context, message *sarama.ConsumerMessage) (enrichedMessage string, topic string, err error) {
	if _, ok := helpers.EnricherDB[message.Topic]; !ok {
		return "", "", errors.New("Invalid topic to enrich")
	}

	enrichedMessage, err = enrich(ctx, string(message.Value), helpers.EnricherDB[message.Topic])
	topic = helpers.EnricherDB[message.Topic].OutputTopic

	return
}
