package helpers

import (
	"context"
	"time"

	"github.com/Zomato/flash-gateway/internal/models"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	"github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/delivery/order_common"
	"github.com/tidwall/gjson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func buildHeader(ctx context.Context, header string) models.Header {
	eventTimestamp := gjson.Get(header, "timestamp").Int()
	if eventTimestamp == 0 {
		eventTimestamp = gjson.Get(header, "time").Int()
	}

	eventTime := time.Unix(eventTimestamp, 0).UTC()
	timestamp := eventTime.Format(time.RFC3339) // converts utc time to RFC3339 format

	statsd.PushEventLag(ComputeEventLag(eventTime), ctx)

	recievedAtTimestamp := gjson.Get(header, "received_at").Int()
	recievedAt := time.Unix(recievedAtTimestamp, 0).UTC().Format(time.RFC3339)

	locationInfo := models.LocationInfo{
		UserDefinedLatitude:  GetFloat64ValueFromJsonString(header, "location_info.user_defined_latitude"),
		UserDefinedLongitude: GetFloat64ValueFromJsonString(header, "location_info.user_defined_longitude"),
		CurrentLongitude:     GetFloat64ValueFromJsonString(header, "location_info.current_longitude"),
		CurrentLatitude:      GetFloat64ValueFromJsonString(header, "location_info.current_latitude"),
	}

	return models.Header{
		Source:        gjson.Get(header, "source").String(),
		DeviceID:      gjson.Get(header, "device_id").String(),
		SessionID:     gjson.Get(header, "session_id").String(),
		UserID:        gjson.Get(header, "user_id").String(),
		UserAgent:     gjson.Get(header, "user_agent").String(),
		IngestionTime: recievedAt,
		Time:          timestamp,
		Location:      gjson.Get(header, "location").String(),
		LocationInfo:  locationInfo,
	}
}

func applyTransformationOperations(transformations map[string]string, transformedMap *map[string]string, rootPayload string) {
	for originalKey, tranformedKey := range transformations {
		(*transformedMap)[tranformedKey] = gjson.Get(rootPayload, "value."+originalKey).String()
	}
}

func applyFilterOperations(filters []Filter, rootPayload string) bool {
	valid := false
	for _, filter := range filters {
		if IsInStringSlice(gjson.Get(rootPayload, "value."+filter.FieldName).String(), filter.Values) {
			valid = true
		} else {
			return false
		}
	}

	return valid
}
func GetSecondsFromTimestamp(timestamp *timestamppb.Timestamp) int64 {
	if timestamp != nil {
		return timestamp.Seconds
	}
	return 0
}

func GetISTTimeFieldsFromTimestamp(timestamp *timestamppb.Timestamp) models.TimeFields {
	loc, err := time.LoadLocation("Asia/Calcutta")
	if err != nil {
		return models.TimeFields{}
	}
	unixTime := time.Unix(GetSecondsFromTimestamp(timestamp), 0).In(loc)
	_, week := unixTime.ISOWeek()
	return models.TimeFields{
		Hour:    unixTime.Hour(),
		Day:     unixTime.Day(),
		ISOWeek: week,
		Month:   int(unixTime.Month()),
		Year:    unixTime.Year(),
		DT:      unixTime.Format("2006-01-02"),
	}
}

func GetEventMealtime(eventHour int) string {
	if eventHour >= 7 && eventHour < 11 {
		return "Breakfast"
	} else if eventHour >= 11 && eventHour < 15 {
		return "Lunch"
	} else if eventHour >= 15 && eventHour < 19 {
		return "Snack"
	} else if eventHour >= 19 && eventHour < 23 {
		return "Dinner"
	} else {
		return "LateNight"
	}

}

func GetDiscountAppliedFlag(discountApplied *order_common.DiscountsApplied) int {
	if discountApplied == nil {
		return 0
	}
	for _, discount := range discountApplied.Discounts {
		if IsInStringSlice(discount.Type, CompositeMvdDiscountTypes) {
			return 1
		}
	}
	return 0
}
