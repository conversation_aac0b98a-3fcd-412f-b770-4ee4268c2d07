package helpers

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"math"
	"os"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/Zomato/go/config"

	gateway "github.com/Zomato/flash-gateway-client-golang/gateway"
	"github.com/Zomato/flash-gateway/internal/models"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	log "github.com/Zomato/go/logger"
	jumboEvent "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/delivery/delivery_composite_order"
	_ "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/delivery/delivery_consumer_order"
	_ "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/delivery/delivery_logistics_order"
	_ "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/delivery/delivery_merchant_order"
	_ "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/delivery/delivery_payment_order"
	zomatoEvent "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/event"
	"github.com/golang/protobuf/jsonpb"
	"github.com/golang/protobuf/ptypes"
	jsoniter "github.com/json-iterator/go"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

var CompositeMvdDiscountTypes = []string{"merchant_voucher_discount",
	"merchant_dc_voucher_discount",
	"merchant_discount",
	"dominos_coupon",
	"salt_discounts",
	"salt_discount",
	"salt_dish_discount",
	"merchant_referral_discount",
	"merchant_user_salt_discount",
	"merchant_pro_discount",
	"restaurant_loyalty_discount"}

// HandlePanic handles pan\c
func HandlePanic(ctx context.Context, err interface{}) {
	statsd.PushPanicCount(ctx)
	log.Warn("panic error", zap.Any("error", err))
	fmt.Fprintf(os.Stderr, "[PANIC] %s ", string(debug.Stack()))
}

// SeparateMessageParts separates header and payload
// format v1: header$%payload
func SeparateMessageParts(value string) (string, []string) {
	messageParts := strings.Split(value, "$%")
	if len(messageParts) < 2 {
		log.Warn("Payload not found", zap.String("value", value))
		return "", nil
	}

	return messageParts[0], messageParts[1:]
}

// IsInStringSlice checks if needle is present in haystack
func IsInStringSlice(needle string, haystack []string) bool {
	for _, event := range haystack {
		if event == needle {
			return true
		}
	}
	return false

}

// ShouldFilter check if the event need to be filter or not
func ShouldFilter(filters []Filter, rootPayload string) bool {
	valid := false
	for _, filter := range filters {
		if IsInStringSlice(gjson.Get(rootPayload, "value."+filter.FieldName).String(), filter.Values) {
			valid = false
		} else {
			return true
		}
	}

	return valid
}

// MergeMapStringInterface returns the union of both given maps
func MergeMapStringInterface(map1, map2 *map[string]interface{}) *map[string]interface{} {
	for key, value := range *map1 {
		(*map2)[key] = value
	}

	return map2
}

// IsJSONHacky return true for valid json strings
// decoding json string takes time, so keeping this
// for faster O(1) computation
func IsJSONHacky(jsonString string) bool {
	return len(jsonString) > 0 && (string(jsonString[0]) == "{" || string(jsonString[0]) == "[")
}
func IsJSONArray(jsonString string) bool {
	return len(jsonString) > 0 && (string(jsonString[0]) == "[")
}

func ConvertToint32(value interface{}) int32 {
	switch value := value.(type) {
	default:
		return value.(int32)
	case string:
		intvalue, err := strconv.ParseInt(value, 0, 32)
		if err != nil {
			log.Error(err.Error())
		}
		return int32(intvalue)

	}
}

func ConvertToString(value interface{}) string {
	switch value := value.(type) {
	default:
		return fmt.Sprintf("%s", value)
	case int:
		return fmt.Sprintf("%s", strconv.FormatInt(int64(value), 10))
	}
}

func GetOptTime(time int) int {
	timeString := strconv.Itoa(time)
	if len(timeString) <= 10 {
		return time * 1000
	}
	return time
}

// UniqueStringSlice returns unique slice
func UniqueStringSlice(slice []string) []string {
	sliceMap := make(map[string]int8)

	for _, element := range slice {
		sliceMap[element] = 1
	}

	uniqueSlice := make([]string, 0)

	for key := range sliceMap {
		uniqueSlice = append(uniqueSlice, key)
	}

	return uniqueSlice
}

func ComputeEventLag(emissionTime time.Time) time.Duration {
	currentTime := time.Now()
	return currentTime.Sub(emissionTime)
}

// GetParsedMessage returns parsed proto message in string
// also extract the topic from message
func GetParsedMessage(ctx context.Context, message *gateway.MessageEnvelope) (string, string, error) {
	var x ptypes.DynamicAny
	if err := ptypes.UnmarshalAny(message.Payload, &x); err != nil {
		return "", "", errors.New("Unable to parse message type. Error: " + err.Error())
	}

	m := jsonpb.Marshaler{EmitDefaults: true}
	result, err := m.MarshalToString(x.Message)
	if err != nil {
		return "", "", errors.New("Unable to marshal message. Error: " + err.Error())
	}

	objectMap := make(map[string]interface{})

	err = json.Unmarshal([]byte(result), &objectMap)
	if err != nil {
		return "", "", errors.New("Unable to parse message. Error: " + err.Error())
	}

	field, ok := objectMap["EventTime"]
	if !ok || field == nil {
		return "", "", fmt.Errorf("EventTime field nil. Object: %v", objectMap)
	}

	eventTime := field.(string)
	t, err := time.Parse(time.RFC3339Nano, eventTime)
	if err != nil {
		return "", "", fmt.Errorf("Unable to parse EventTime, field: %v, Object: %v", field, objectMap)
	}

	statsd.PushEventLag(ComputeEventLag(t), ctx)

	objectMap["EventTime"] = t.Format("2006-01-02T15:04:05Z")

	namespace, ok1 := objectMap["Namespace"]
	schemaName, ok2 := objectMap["SchemaName"]

	var topic string
	if ok1 && ok2 {
		topic = namespace.(string) + "." + schemaName.(string)
	} else {
		topic = config.GetString(ctx, "kafka.notdefinedtopic")
		log.Warn("Topic Name not Defined", zap.Any("objectMap", objectMap))
	}

	if topic == "" {
		return "", "", errors.New("Unable to get topic name")
	}

	r, err := json.Marshal(objectMap)
	if err != nil {
		return "", "", fmt.Errorf("Unable to marshal objectMap. Object: %v", objectMap)
	}

	return string(r), topic, err
}

func GetSHA256Hashed(input string) string {
	if input == "" {
		return ""
	}
	sum := sha256.Sum256([]byte(input))
	return hex.EncodeToString(sum[:])
}

func GetZomatoEventParsedMessage(message *zomatoEvent.Event) (string, error, string) {
	pb := &delivery_composite_order.Order{}
	if err := ptypes.UnmarshalAny(message.Payload, pb); err != nil {
		return "", errors.New("Unable to parse message type. Error: " + err.Error()), ""
	}
	consumerOrderPhoneNumber := pb.GetConsumerOrder().GetContact().GetPhone()
	consumerOrderAliasName := pb.GetConsumerOrder().GetContact().GetAliasName()
	consumerOrderPhoneHex := GetSHA256Hashed(consumerOrderPhoneNumber)
	consumerCreatedISTTimeFields := GetISTTimeFieldsFromTimestamp(pb.GetConsumerOrder().GetCreatedAt())
	merchantCreatedISTTimeFields := GetISTTimeFieldsFromTimestamp(pb.GetMerchantOrder().GetCreatedAt())
	compositeEvent := &models.CompositeEvents{
		ConsumerOrderID:                                           pb.GetConsumerOrder().GetId(),
		ConsumerOrderCurrency:                                     pb.GetConsumerOrder().GetCurrency(),
		ConsumerOrderResID:                                        pb.GetConsumerOrder().GetResId(),
		ConsumerOrderAppVersion:                                   pb.GetConsumerOrder().GetClientDetails().GetAppVersion(),
		ConsumerOrderState:                                        pb.GetConsumerOrder().GetState().String(),
		ConsumerOrderUserID:                                       pb.GetConsumerOrder().GetUser().GetUserId(),
		ConsumerOrderDeliveryMode:                                 pb.GetConsumerOrder().GetDeliveryMode().String(),
		ConsumerOrderTestOrder:                                    pb.GetConsumerOrder().GetTestOrder(),
		ConsumerOrderPosVersion:                                   pb.GetConsumerOrder().GetPosDetails().GetPosVersion(),
		ConsumerOrderVendorID:                                     pb.GetConsumerOrder().GetPosDetails().GetVendorId(),
		ConsumerOrderTotalQuantity:                                pb.GetConsumerOrder().GetTotal().GetQuantity(),
		ConsumerOrderTotalCost:                                    pb.GetConsumerOrder().GetSubtotal().GetAmountTotalCost(),
		ConsumerOrderTotalUnitCost:                                pb.GetConsumerOrder().GetSubtotal().GetAmountUnitCost(),
		ConsumerOrderCancelReasonID:                               pb.GetConsumerOrder().GetCancelReason().GetId(),
		ConsumerOrderCancelledAt:                                  GetSecondsFromTimestamp(pb.GetConsumerOrder().GetCancelledAt()),
		ConsumerOrderIsOtof:                                       pb.GetConsumerOrder().GetIsOtof(),
		ConsumerOrderCreatedAt:                                    GetSecondsFromTimestamp(pb.GetConsumerOrder().GetCreatedAt()),
		ConsumerOrderCreatedISTHour:                               consumerCreatedISTTimeFields.Hour,
		ConsumerOrderCreatedISTDay:                                consumerCreatedISTTimeFields.Day,
		ConsumerOrderCreatedISTISOWeek:                            consumerCreatedISTTimeFields.ISOWeek,
		ConsumerOrderCreatedISTMonth:                              consumerCreatedISTTimeFields.Month,
		ConsumerOrderCreatedISTYear:                               consumerCreatedISTTimeFields.Year,
		ConsumerOrderCreatedISTDt:                                 consumerCreatedISTTimeFields.DT,
		ConsumerOrderMealtime:                                     GetEventMealtime(consumerCreatedISTTimeFields.Hour),
		ConsumerOrderDiscountAppliedFlag:                          GetDiscountAppliedFlag(pb.GetConsumerOrder().GetDiscountApplied()),
		MerchantOrderState:                                        pb.GetMerchantOrder().GetState().String(),
		MerchantOrderRejectReasonID:                               pb.GetMerchantOrder().GetRejectReason().GetId(),
		MerchantOrderUserID:                                       pb.GetMerchantOrder().GetUser().GetUserId(),
		MerchantOrderDeliveryMode:                                 pb.GetMerchantOrder().GetDeliveryMode().String(),
		MerchantOrderDeliveryTime:                                 pb.GetMerchantOrder().GetDeliveryTime(),
		MerchantOrderResID:                                        pb.GetMerchantOrder().GetResId(),
		MerchantOrderCreatedAt:                                    GetSecondsFromTimestamp(pb.GetMerchantOrder().GetCreatedAt()),
		MerchantOrderForMarkedStatus:                              pb.GetMerchantOrder().GetForMarkedStatus().String(),
		MerchantOrderKptDelaySecs:                                 pb.GetMerchantOrder().GetKptDelaySecs(),
		MerchantOrderTotalCost:                                    pb.GetMerchantOrder().GetTotal().GetAmountTotalCost(),
		MerchantOrderRejectReason:                                 pb.GetMerchantOrder().GetRejectReason().String(),
		LogisticsOrderID:                                          pb.GetLogisticsOrder().GetId(),
		LogisticsPartnerID:                                        pb.GetLogisticsOrder().GetLogisticsPartnerId(),
		LogisticsOrderState:                                       pb.GetLogisticsOrder().GetState().String(),
		LogisticsOrderCreatedAt:                                   GetSecondsFromTimestamp(pb.GetLogisticsOrder().GetCreatedAt()),
		LogisticsOrderUpdatedAt:                                   GetSecondsFromTimestamp(pb.GetLogisticsOrder().GetUpdatedAt()),
		UpdatedAt:                                                 pb.GetUpdatedAt().String(),
		MerchantOrderCreatedISTHour:                               merchantCreatedISTTimeFields.Hour,
		MerchantOrderCreatedISTDay:                                merchantCreatedISTTimeFields.Day,
		MerchantOrderCreatedISTISOWeek:                            merchantCreatedISTTimeFields.ISOWeek,
		MerchantOrderCreatedISTMonth:                              merchantCreatedISTTimeFields.Month,
		MerchantOrderCreatedISTYear:                               merchantCreatedISTTimeFields.Year,
		MerchantOrderCreatedISTDt:                                 merchantCreatedISTTimeFields.DT,
		MerchantOrderMealtime:                                     GetEventMealtime(merchantCreatedISTTimeFields.Hour),
		ConsumerOrderRating:                                       int(pb.GetConsumerOrder().GetUserRating().GetRating()),
		IngestionTime:                                             time.Now().UnixNano(),
		ConsumerOrderUserProfileSelectedPrimaryId:                 pb.GetConsumerOrder().GetUser().GetUserProfiles().GetSelectedProfile().GetPrimaryId(),
		ConsumerOrderUserProfileSelectedType:                      pb.GetConsumerOrder().GetUser().GetUserProfiles().GetSelectedProfile().GetType().String(),
		ConsumerOrderIsInstantOrder:                               pb.GetConsumerOrder().GetIsInstantOrder(),
		ConsumerOrderIsInterCityOrder:                             pb.GetConsumerOrder().GetIsIntercityOrder(),
		ConsumerOrderFulfilmentFulfilmentType:                     pb.GetConsumerOrder().GetFulfilment().GetFulfilmentType().String(),
		ConsumerOrderFulfilmentLogisticsFulfilmentType:            pb.GetConsumerOrder().GetFulfilment().GetLogistics().GetFulfilmentType().String(),
		ConsumerOrderFulfilmentLogisticsShipmentSourceCityId:      pb.GetConsumerOrder().GetFulfilment().GetLogistics().GetShipmentSource().GetCityId(),
		ConsumerOrderFulfilmentLogisticsShipmentDestinationCityId: pb.GetConsumerOrder().GetFulfilment().GetLogistics().GetShipmentDestination().GetCityId(),
		ConsumerOrderTotalAmountTotalCost:                         pb.GetConsumerOrder().GetTotal().GetAmountTotalCost(),
		ConsumerOrderPaymentsPrimaryPaymentPaymentMethodPaymentMethodType: pb.GetConsumerOrder().GetPayments().GetPrimaryPayment().GetPaymentMethod().GetPaymentMethodType().String(),
		Revision:        pb.GetRevision(),
		MerchantOrderID: pb.GetMerchantOrder().GetId(),
		ConsumerOrderFulfilmentLogisticsLogisticsPartnerId: pb.GetConsumerOrder().GetFulfilment().GetLogistics().GetLogisticsPartnerId(),
		ConsumerOrderDeliverySpeed:                         pb.GetConsumerOrder().GetDeliverySpeed().String(),
		MerchantOrderStateHistoryReadyTime:                 GetSecondsFromTimestamp(pb.GetMerchantOrder().GetStateHistory().GetReady().GetTime()),
		MerchantOrderStateHistoryAcceptedTime:              GetSecondsFromTimestamp(pb.GetMerchantOrder().GetStateHistory().GetAccepted().GetTime()),
		LogisticsOrderStateHistoryArrivedTime:              GetSecondsFromTimestamp(pb.GetLogisticsOrder().GetStateHistory().GetArrived().GetTime()),
		LogisticsOrderStateHistoryPickedTime:               GetSecondsFromTimestamp(pb.GetLogisticsOrder().GetStateHistory().GetPicked().GetTime()),
		ConsumerOrderPhoneHash:                             consumerOrderPhoneHex,
		ConsumerOrderPhoneNumberAliasName:                  consumerOrderAliasName,
		ShipmentDestinationAliasName:                       pb.GetConsumerOrder().GetFulfilment().GetLogistics().GetShipmentDestination().GetLocationDetails().GetAlias(),
	}
	compositeEventString, err := json.MarshalToString(compositeEvent)

	return compositeEventString, err, strconv.FormatInt(pb.GetConsumerOrder().GetId(), 10)
}

func GetHeaderAppInfoDriverPerformance(jsonString string, path string) jumboEvent.DevicePerformance {
	pathValue := gjson.Get(jsonString, path)
	if !pathValue.Exists() {
		return jumboEvent.DevicePerformance_DEVICE_PERFORMANCE_UNSPECIFIED
	}
	driverPerformanceRawStr := pathValue.String()
	driverPerformanceFormattedStr := strings.ToUpper(driverPerformanceRawStr)
	// Using switch instead of directly accessing key from ENum Map due to possible conflicts.
	switch driverPerformanceFormattedStr {
	case "LOW":
		return jumboEvent.DevicePerformance_LOW
	case "AVERAGE":
		return jumboEvent.DevicePerformance_AVERAGE
	case "HIGH":
		return jumboEvent.DevicePerformance_HIGH
	default:
		return jumboEvent.DevicePerformance_DEVICE_PERFORMANCE_UNSPECIFIED
	}
}

func GetMultipleValueResults(message string, id1 string, id2 string) gjson.Result {
	result := gjson.Get(message, id1)
	if !result.Exists() {
		result = gjson.Get(message, id2)
	}
	return result
}

func FetchConfig(ctx context.Context, key string, tenant string) interface{} {
	defer func() interface{} {
		if err := recover(); err != nil {
			log.Println("config not found for %v, resorting to default value", fmt.Sprintf(key, tenant))
			return config.Get(ctx, fmt.Sprintf(key, "default"))
		}

		return nil
	}()
	return config.Get(ctx, fmt.Sprintf(key, tenant))
}

func GetFloat64ValueFromJsonString(jsonString string, path string) *float64 {
	pathValue := gjson.Get(jsonString, path)
	if !pathValue.Exists() {
		defaultNilFloatValue := 0.0
		return &defaultNilFloatValue
	}
	floatValue := pathValue.Float()
	return &floatValue
}

// Added as a separate function, to avoid reflection overhead
func GetUInt32ValueFromJsonString(jsonString string, path string) uint32 {
	pathValue := gjson.Get(jsonString, path)
	if !pathValue.Exists() {
		return 0
	}
	// As gjson.Result supports reflection into uint64
	intValue := pathValue.Uint()
	if intValue > math.MaxUint32 {
		return math.MaxUint32
	}
	uint32Value := uint32(intValue)
	return uint32Value
}
