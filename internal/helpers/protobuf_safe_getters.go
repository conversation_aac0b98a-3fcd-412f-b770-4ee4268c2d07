package helpers

import (
	"google.golang.org/protobuf/types/known/wrapperspb"
)

// Pointer-returning versions for struct with omitempty support
// These return typed pointers instead of interface{}, allowing proper omitempty behavior

// GetSafeStringPtr returns nil if the StringValue wrapper is nil, otherwise returns a pointer to the actual value.
func GetSafeStringPtr(wrapper *wrapperspb.StringValue) *string {
	if wrapper == nil {
		return nil
	}
	value := wrapper.GetValue()
	return &value
}

// GetSafeInt32Ptr returns nil if the Int32Value wrapper is nil, otherwise returns a pointer to the actual value.
func GetSafeInt32Ptr(wrapper *wrapperspb.Int32Value) *int32 {
	if wrapper == nil {
		return nil
	}
	value := wrapper.GetValue()
	return &value
}

// GetSafeInt64Ptr returns nil if the Int64Value wrapper is nil, otherwise returns a pointer to the actual value.
func GetSafeInt64Ptr(wrapper *wrapperspb.Int64Value) *int64 {
	if wrapper == nil {
		return nil
	}
	value := wrapper.GetValue()
	return &value
}

// GetSafeUInt32Ptr returns nil if the UInt32Value wrapper is nil, otherwise returns a pointer to the actual value.
func GetSafeUInt32Ptr(wrapper *wrapperspb.UInt32Value) *uint32 {
	if wrapper == nil {
		return nil
	}
	value := wrapper.GetValue()
	return &value
}

// GetSafeDoublePtr returns nil if the DoubleValue wrapper is nil, otherwise returns a pointer to the actual value.
func GetSafeDoublePtr(wrapper *wrapperspb.DoubleValue) *float64 {
	if wrapper == nil {
		return nil
	}
	value := wrapper.GetValue()
	return &value
}

// GetSafeBoolPtr returns nil if the BoolValue wrapper is nil, otherwise returns a pointer to the actual value.
func GetSafeBoolPtr(wrapper *wrapperspb.BoolValue) *bool {
	if wrapper == nil {
		return nil
	}
	value := wrapper.GetValue()
	return &value
}

// GetSafeStringSlicePtr returns nil if the slice is nil, otherwise returns the actual slice (for omitempty)
func GetSafeStringSlicePtr(vals []*wrapperspb.StringValue) []string {
	if vals == nil {
		return nil
	}
	result := make([]string, len(vals))
	for i, val := range vals {
		if val != nil {
			result[i] = val.GetValue()
		}
		// nil values in the slice will remain as empty strings
	}
	return result
}
