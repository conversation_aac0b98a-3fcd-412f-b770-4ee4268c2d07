logger:
  level: 0
  timestamp: ""

profiling:
  enabled: false
  port: 6060

datadog:
  profiling:
    enabled: 'false'
    goroutine_profile:
      enabled: 'false'

kafka:
  notdefinedtopic: dataplatform.undefined_events_stag
  compositeeventsoutputtopic: output-topic
  consumers:
    jumbo:
      brokers: kafka:29092
      topics: searchTopic
      group: jumbo_flash_router_stag
      version: 2.4.1
    blinkit:
      brokers: kafka:29092
      topics: searchTopic
      group: jumbo_blinkit_router_stag
      version: 2.4.1
    aggr:
      brokers: kafka:29092
      topics: logistics.enveloped_events_stag
      group: any-worker-stag
      version: 2.5.0
    flash:
      brokers: kafka:29092
      topics: noob
      group: jumbo_flash_router_stag
      version: 2.2.0
    enrichmentingestor:
      brokers: kafka:29092
      topics: noob
      group: jumbo_flash_router_stag
      version: 2.2.0
    alertconsumer:
      brokers: kafka:29092
      topics: noob
      group: alerts-consumer_stag
      version: 2.2.0
    protoconvertorconsumerbackend:
      brokers: kafka:29092
      topics: proto-conv-test
      group: protoc_test_2
      version: 2.2.0
    protoconvertorconsumerfrontend:
      brokers: kafka:29092
      topics: proto-conv-test
      group: protoc_test_2
      version: 2.2.0
    jumbov2:
      brokers: kafka:29092
      topics: proto-conv-test
      group: protoc_test_2
      version: 2.2.0
    compositeconsumer:
      brokers: kafka:29092
      topics: noob
      group: testing123
      version: "2.2.0"
    jumbov2eventconsumer:
      brokers: kafka
      topics: noodd
      topics_regex: test.*
      group: testing_123
      version: "2.2.0"
    blinkitorderlifecycle:
      brokers: kafka:29092
      topics: clevertap
      group: clevertap_test_group
      version: "2.2.0"
      destinations:
        clevertap: blinkit
    zomato-jumbo-consumer-frontend:
      brokers: kafka:29092
      topics: searchTopic
      group: jumbo_flash_router_stag
      version: 2.4.1     
    zomato-jumbo-consumer-backend:
      brokers: kafka:29092
      topics: searchTopic
      group: jumbo_flash_router_stag
      version: 2.4.1  
    blinkit-jumbo-consumer-frontend:
      brokers: kafka:29092
      topics: searchTopic
      group: jumbo_flash_router_stag
      version: 2.4.1  
    blinkit-jumbo-consumer-backend:
      brokers: kafka:29092
      topics: searchTopic
      group: jumbo_flash_router_stag
      version: 2.4.1    
    blinkitprotoconvertorconsumerfrontend:
      brokers: kafka:29092
      topics: blinkit_search_topic_v2
      group: flash_blinkit_protoconvertor
      version: 2.2.0
    blinkitprotoconvertorconsumerbackend:
      brokers: kafka:29092
      topics: blinkit_search_topic_v2
      group: flash_blinkit_protoconvertor
      version: 2.2.0
  producers:
    default:
      brokers: kafka
    offlinev2:
      brokers: kafka:29092
    sdp:
      brokers: kafka:29092
    staging:
      brokers: kafka:29092
    composite:
      brokers: kafka:29092
  config:
    producer:
      flushfrequency: 500ms

clevertap:
  producers:
    default:
      workerpool:
        size: 10
        batchSize: 10
        flushinterval: 5s
        queuelength: 8
    blinkit:
      accountid: SAMPLE-ACCOUNT-ID
      passcode: samplepasscode
      endpoint: https://api.clevertap.com
      workerpool:
        size: 10
        batchSize: 10
        flushinterval: 5s
        queuelength: 8

statsd:
  url: 127.0.0.1:9125
  application: flash_gateway
  job: jumbo

notify:
  url: localhost

cache:
  metrics: true
  l2:
    ttl: 24h
    numcounters: ********
    maxcost: 1000000
  l1:
    hosts: 127.0.0.1:6379
    clustermode: false
    poolsize: 1
    servereadsfromslaves: true

enricher:
  kafka: default

proto-convertor:
  kafka: staging
  priority-kafka: staging

composite:
  kafka: composite

newrelic:
  application: flash_gateway
  license: ""
  enabled: false

workerpool:
  size: 10
  batchSize: 10
  flushinterval: 5s
  queuelength: 8

profiler:
  port: 6060
  enabled: false
  credentials: ""
  goroutine_profile:
    enabled: false
  block_profile:
    enabled: false
  mutex_profile:
    enabled: false
  gc_runs:
    disabled: true

go_runtime:
  mem_limit:
    enabled: false
    go_gc_percentage: -1
  metrics:
    enabled: false