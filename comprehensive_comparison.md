
# Comprehensive Schema Comparison

## Main Structure Comparison

### Proto ImpressionEvents Message
```protobuf
message ImpressionEvents {
    google.protobuf.StringValue event_name = 1;
    Traits traits = 2;
    Properties properties = 3;
}
```

### Go ImpressionEventData Struct
```go
type ImpressionEventData struct {
    // Additional wrapper fields not in proto
    Source             string
    DeviceID           string
    SessionID          string
    UserID             string
    UserAgent          string
    Timestamp          int64
    Time               int64
    IngestionTimestamp int64
    Location           uint32
    AppInfo            AppInfoData
    
    // Proto fields
    EventName          string
    Traits             ImpressionTraitsData
    Properties         ImpressionPropertiesData
    
    // Additional wrapper fields
    URL                string
    EventID            string
    SequenceID         string
    SequenceOffset     int64
}
```

**Finding**: The Go model has additional wrapper fields for event metadata that are not in the proto. This is expected for data pipeline processing.

---

## Properties Comparison (253 fields)

✅ **All 253 proto Properties fields are now present in Go model** (including the recently added `shipment_id`)

---

## Traits Comparison (33 fields)

### Proto Traits (fields 1-33):
1. app_flavor
2. cart_id
3. chain_id
4. channel
5. city_id
6. city_name
7. device_uuid
8. install_campaign
9. install_source
10. latitude
11. lifetime_orders
12. longitude
13. merchant_id
14. merchant_name
15. monthly_orders
16. segment_enabled_features
17. session_launch_source
18. session_uuid
19. total_order_value
20. user_type
21. app_version_code
22. user_experiment_buckets
23. install_medium
24. install_referrer
25. is_default_merchant
26. tracking_id
27. appsflyer_app_instance_id
28. location_hex
29. host_app_type
30. host_app_version
31. host_app_version_code
32. host_app_user_id
33. segment_type

### Go ImpressionTraitsData (33 fields):
✅ All proto traits fields are present

---

## Nested Structures

### 1. ProductsInShipment
**Proto**: `google.protobuf.Int64Value product_id = 1;`
**Go**: `ProductID *int64 `json:"product_id,omitempty"``
✅ **Match**

### 2. Products
**Proto fields 1-16** (field 12 is missing in proto sequence - jumps from 11 to 13)
**Go fields**: All present, matching proto structure
✅ **Match**

### 3. Shipments
**Proto fields 1-8**
**Go fields**: All present
✅ **Match**

### 4. ProductAlternatives
**Proto**: `pid = 1`, `alternatives = 2`
**Go**: `PID`, `Alternatives`
✅ **Match**

### 5. Merchant
**Proto fields 1-5**
**Go fields**: All present
✅ **Match**

### 6. OOSProduct
**Proto**: `reason = 1`, `id = 2`
**Go**: `Reason`, `ID`
✅ **Match**

### 7. InputType Enum
**Proto**: 
```protobuf
enum InputType {
    INPUT_TYPE_UNSPECIFIED = 0;
    AUTO = 1;
    MANUAL = 2;
}
```

**Go**:
```go
type InputType int32
const (
    InputType_AUTO   InputType = 0  // ❌ Should be 1
    InputType_MANUAL InputType = 1  // ❌ Should be 2
)
```

**❌ ISSUE FOUND**: InputType enum values are incorrect in Go model!

---

## Data Type Mismatches

Checking critical data type mappings:

1. **Proto Products.currency field**:
   - Proto: `google.protobuf.StringValue currency = 13;` (field 12 is missing)
   - Go: `Currency *string` 
   ✅ **Match**

2. **Proto Products field numbering**:
   - Proto jumps from `type_id = 11` to `currency = 13` (field 12 is missing)
   - This is a proto design issue, not a sync issue

---

## Summary of Issues Found

### 🚨 Critical Issue:
**InputType enum values are incorrect in Go model**
- Proto: `AUTO = 1`, `MANUAL = 2` 
- Go: `InputType_AUTO = 0`, `InputType_MANUAL = 1`

### ✅ Everything else is in sync:
- All 253 Properties fields present
- All 33 Traits fields present  
- All nested structures match
- Data types are correctly mapped
