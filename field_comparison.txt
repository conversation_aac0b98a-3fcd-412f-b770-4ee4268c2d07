
Proto Properties Fields (1-253):
1. products
2. shipments
3. child_widget_id
4. child_widget_name
5. child_widget_position
6. child_widget_revision_id
7. child_widget_title
8. child_widget_tracking_id
9. entry_source_child_id
10. entry_source_child_name
11. entry_source_child_position
12. entry_source_child_title
13. entry_source_child_tracking_id
14. entry_source_child_variation_id
15. entry_source_id
16. entry_source_name
17. entry_source_position
18. entry_source_revision_id
19. entry_source_title
20. entry_source_tracking_id
21. entry_source_variation_id
22. is_react_page
23. last_page_id
24. last_page_name
25. last_page_revision_id
26. last_page_title
27. last_page_tracking_id
28. last_page_variation_id
29. last_page_visit_id
30. last_sub_page_name
31. last_sub_page_title
32. last_sub_page_visit_id
33. page_id
34. page_name
35. page_revision_id
36. page_title
37. page_tracking_id
38. page_variation_id
39. page_visit_id
40. sub_page_name
41. sub_page_title
42. sub_page_visit_id
43. widget_tracking_id
44. widget_variation_id
45. address_count
46. address_id
47. address_type
48. ads_asset_type_id
49. ads_campaign_id
50. ads_collection_id
51. ads_subcampaign_id
52. ads_type
53. aerobar_id
54. amount
55. app_installed
56. available_update_version
57. badge
58. brand
59. button_text
60. campaign
61. campaign_id
62. campaign_identifier
63. card_type
64. cart_id
65. cart_savings
66. cart_type
67. cart_value
68. checkbox_state
69. child_widget_impression_count
70. click_source
71. collection_id
72. coupon_amount
73. coupon_code
74. coupon_discount_value
75. coupon_type
76. cta_type
77. cumulative_delay
78. currency
79. current_cart_savings
80. current_object_id
81. custom_data
82. data1
83. data2
84. data3
85. data4
86. deeplink
87. device_lat
88. device_lon
89. dialog_type
90. enabled
91. error_description
92. event_source_identifier
93. favourite_icon_state
94. filter_keys
95. filter_position
96. filters_present
97. icon
98. id
99. image_url
100. images_shown
101. images_shown_flags
102. invalid_ids
103. inventory
104. inventory_limit
105. is_checked
106. is_coupon_applicable
107. is_earliest_slot
108. items_in_cart
109. l0_category
110. l1_category
111. l2_category
112. label
113. latitude
114. location_fetch_accuracy
115. location_fetch_altitude
116. location_update_type
117. longitude
118. merchant_id
119. merchant_type
120. message
121. mrp
122. name
123. new_locality
124. next_object_id
125. notification_type
126. number_of_files
127. number_of_products
128. offer
129. offer_text
130. old_locality
131. oos_pid_list
132. order_count
133. order_hash_id
134. order_id
135. order_state
136. order_status
137. overlay_badges
138. overlay_present
139. page_type
140. payload
141. payment_method
142. payment_mode
143. popup_type
144. position
145. post_change_latitude
146. post_change_longitude
147. prev_label
148. price
149. product_alternatives_list
150. product_count
151. product_id
152. product_ids
153. product_list_id
154. product_position
155. products_in_shipment
156. promo_identifers
157. ptype
158. quantity
159. rating
160. reason
161. recommendation_id
162. sbc_cart_savings
163. sdk_version
164. search_actual_keyword
165. search_input_keyword
166. search_keyword_parent
167. search_keyword_type
168. search_previous_keyword
169. search_result_count
170. search_user_typed_input_keyword
171. selected
172. selected_items
173. selected_tab
174. service_type
175. shared_id
176. shipment_id
177. shipment_type
178. shipment_value
179. shipping
180. slot_amount
181. slot_date
182. slot_time
183. source
184. ssid
185. state
186. status
187. sub_page_id
188. sub_page_revision_id
189. subcategory_id
190. subtitle
191. subtitle_tag
192. suggested_keywords
193. suggestion_position
194. suggestion_source
195. suggestion_type
196. suggestion_value
197. swipe_direction
198. time_to_delivery_in_mins
199. tip_amount
200. title
201. total
202. total_items
203. total_products_in_cart
204. total_products_in_shared_cart
205. total_products_in_shipment
206. total_products_out_of_stock
207. total_shipments_in_cart
208. type
209. unique_products
210. unique_products_in_cart
211. unique_products_in_shipment
212. unique_products_out_of_stock
213. url
214. widget_id
215. widget_impression_count
216. widget_name
217. widget_position
218. widget_revision_id
219. widget_title
220. widget_type
221. ads_cost_id
222. merchants
223. oos_products
224. offer_code
225. section_tracking_id
226. next_available_at
227. is_donation_given
228. product_state
229. last_sub_page_id
230. watched_duration
231. duration
232. is_surprise_order
233. is_items_hidden
234. highlight_ids
235. widget_group_tracking_id
236. entry_source_group_tracking_id
237. video_shown
238. is_otp_detected
239. input_type
240. birth_date
241. preference
242. is_bottom_sheet_currently_shown
243. has_bottom_sheet_been_shown_before
244. is_address_selected_manually
245. distance
246. coupon_id
247. error_type
248. error_message
249. eta_identifier
250. wishlist_added
251. login_method
252. is_low_power_mode
253. is_accessibility_enabled

Go Model Fields (252 total in ImpressionPropertiesData):
1. Products
2. Shipments
3. ChildWidgetID
4. ChildWidgetName
5. ChildWidgetPosition
6. ChildWidgetRevisionID
7. ChildWidgetTitle
8. ChildWidgetTrackingID
9. EntrySourceChildID
10. EntrySourceChildName
11. EntrySourceChildPosition
12. EntrySourceChildTitle
13. EntrySourceChildTrackingID
14. EntrySourceChildVariationID
15. EntrySourceID
16. EntrySourceName
17. EntrySourcePosition
18. EntrySourceRevisionID
19. EntrySourceTitle
20. EntrySourceTrackingID
21. EntrySourceVariationID
22. IsReactPage
23. LastPageID
24. LastPageName
25. LastPageRevisionID
26. LastPageTitle
27. LastPageTrackingID
28. LastPageVariationID
29. LastPageVisitID
30. LastSubPageName
31. LastSubPageTitle
32. LastSubPageVisitID
33. PageID
34. PageName
35. PageRevisionID
36. PageTitle
37. PageTrackingID
38. PageVariationID
39. PageVisitID
40. SubPageName
41. SubPageTitle
42. SubPageVisitID
43. WidgetTrackingID
44. WidgetVariationID
45. AddressCount
46. AddressID
47. AddressType
48. AdsAssetTypeID
49. AdsCampaignID
50. AdsCollectionID
51. AdsSubcampaignID
52. AdsType
53. AerobarID
54. Amount
55. AppInstalled
56. AvailableUpdateVersion
57. Badge
58. Brand
59. ButtonText
60. Campaign
61. CampaignID
62. CampaignIdentifier
63. CardType
64. CartID
65. CartSavings
66. CartType
67. CartValue
68. CheckboxState
69. ChildWidgetImpressionCount
70. ClickSource
71. CollectionID
72. CouponAmount
73. CouponCode
74. CouponDiscountValue
75. CouponType
76. CTAType
77. CumulativeDelay
78. Currency
79. CurrentCartSavings
80. CurrentObjectID
81. CustomData
82. Data1
83. Data2
84. Data3
85. Data4
86. Deeplink
87. DeviceLat
88. DeviceLon
89. DialogType
90. Enabled
91. ErrorDescription
92. EventSourceIdentifier
93. FavouriteIconState
94. FilterKeys
95. FilterPosition
96. FiltersPresent
97. Icon
98. ID
99. ImageURL
100. ImagesShown
101. ImagesShownFlags
102. InvalidIds
103. Inventory
104. InventoryLimit
105. IsChecked
106. IsCouponApplicable
107. IsEarliestSlot
108. ItemsInCart
109. L0Category
110. L1Category
111. L2Category
112. Label
113. Latitude
114. LocationFetchAccuracy
115. LocationFetchAltitude
116. LocationUpdateType
117. Longitude
118. MerchantID
119. MerchantType
120. Message
121. MRP
122. Name
123. NewLocality
124. NextObjectID
125. NotificationType
126. NumberOfFiles
127. NumberOfProducts
128. Offer
129. OfferText
130. OldLocality
131. OosPidList
132. OrderCount
133. OrderHashID
134. OrderID
135. OrderState
136. OrderStatus
137. OverlayBadges
138. OverlayPresent
139. PageType
140. Payload
141. PaymentMethod
142. PaymentMode
143. PopupType
144. Position
145. PostChangeLatitude
146. PostChangeLongitude
147. PrevLabel
148. Price
149. ProductAlternativesList
150. ProductCount
151. ProductID
152. ProductIds
153. ProductListID
154. ProductPosition
155. ProductsInShipment
156. PromoIdentifers
157. PType
158. Quantity
159. Rating
160. Reason
161. RecommendationID
162. SBCCartSavings
163. SdkVersion
164. SearchActualKeyword
165. SearchInputKeyword
166. SearchKeywordParent
167. SearchKeywordType
168. SearchPreviousKeyword
169. SearchResultCount
170. SearchUserTypedInputKeyword
171. Selected
172. SelectedItems
173. SelectedTab
174. ServiceType
175. SharedID
-- MISSING: shipment_id (proto field 176)
176. ShipmentType
177. ShipmentValue
178. Shipping
179. SlotAmount
180. SlotDate
181. SlotTime
182. Source
183. SSID
184. State
185. Status
186. SubPageID
187. SubPageRevisionID
188. SubcategoryID
189. Subtitle
190. SubtitleTag
191. SuggestedKeywords
192. SuggestionPosition
193. SuggestionSource
194. SuggestionType
195. SuggestionValue
196. SwipeDirection
197. TimeToDeliveryInMins
198. TipAmount
199. Title
200. Total
201. TotalItems
202. TotalProductsInCart
203. TotalProductsInSharedCart
204. TotalProductsInShipment
205. TotalProductsOutOfStock
206. TotalShipmentsInCart
207. Type
208. UniqueProducts
209. UniqueProductsInCart
210. UniqueProductsInShipment
211. UniqueProductsOutOfStock
212. URL
213. WidgetID
214. WidgetImpressionCount
215. WidgetName
216. WidgetPosition
217. WidgetRevisionID
218. WidgetTitle
219. WidgetType
220. AdsCostID
221. Merchants
222. OOSProducts
223. OfferCode
224. SectionTrackingID
225. NextAvailableAt
226. IsDonationGiven
227. ProductState
228. LastSubPageID
229. WatchedDuration
230. Duration
231. IsSurpriseOrder
232. IsItemsHidden
233. HighlightIDs
234. WidgetGroupTrackingID
235. EntrySourceGroupTrackingID
236. VideoShown
237. IsOtpDetected
238. InputType
239. BirthDate
240. Preference
241. IsBottomSheetCurrentlyShown
242. HasBottomSheetBeenShownBefore
243. IsAddressSelectedManually
244. Distance
245. CouponID
246. ErrorType
247. ErrorMessage
248. EtaIdentifier
249. WishlistAdded
250. LoginMethod
251. IsLowPowerMode
252. IsAccessibilityEnabled

MISSING FIELD: shipment_id (proto field 176) is missing from the Go model!
