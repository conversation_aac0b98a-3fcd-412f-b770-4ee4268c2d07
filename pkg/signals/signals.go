package signals

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	heart "github.com/Zomato/flash-gateway/pkg/heartbeat"
	"github.com/Zomato/flash-gateway/pkg/services/cache"
	"github.com/Zomato/flash-gateway/pkg/services/kafka"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	"github.com/Zomato/flash-gateway/pkg/signals/channels"
	"github.com/Zomato/flash-gateway/pkg/workerpool"
	log "github.com/Zomato/go/logger"
	"go.uber.org/zap"
)

// Handler safely shutdowns everything
// on os.kill or os.interrupt or syscall.SIGINT or syscall.SIGTERM
func Handler(ctx context.Context) {
	s := make(chan os.Signal, 1)
	signal.Notify(s, syscall.SIGINT, syscall.SIGTERM, os.Interrupt)

	sig := <-s
	log.Info("Quit signal received, gracefully shutting down goroutines...", zap.Any("signal", sig))

	// Broadcast the shutdown event
	channels.ShutdownChannel <- true

	statsd.Close()
	heart.StopBeating(ctx)

	<-channels.ConsumerShutdownComplete

	kafka.Shutdown()
	time.Sleep(1 * time.Second)
	workerpool.Stop()
	cache.Shutdown()

	channels.CompleteShutdown <- true
}
