package kafka

import (
	"context"
	"strings"
	"sync"
	"time"

	"github.com/Shopify/sarama"
	"github.com/Zomato/go/config"

	"github.com/Zomato/flash-gateway/pkg/signals/channels"
	log "github.com/Zomato/go/logger"

	"regexp"

	"go.uber.org/zap"
)

// Consumer represents consumer object
type Consumer struct {
	Brokers         []string
	Topics          []string
	TopicsRegex     string
	ConsumerGroupID string
	ConsumerGroup   ConsumerGroup
	config          *sarama.Config
	Label           string
}

// NewConsumer returns kafka consumer object
func NewConsumer(label string, ctx context.Context) Consumer {
	if label == "" {
		log.Errorf("Invalid kafka consumer label supplied")
	}

	consumer := Consumer{
		Label: label,
	}

	consumer.setup(ctx)

	log.Info("Created kafka consumer", zap.Any("topics", consumer.Topics))

	return consumer
}

func (c *Consumer) init(ctx context.Context) {
	c.Brokers = strings.Split(config.GetString(ctx, "kafka.consumers."+c.Label+".brokers"), ",")
	c.ConsumerGroupID = config.GetString(ctx, "kafka.consumers."+c.Label+".group")
	c.Topics = strings.Split(config.GetString(ctx, "kafka.consumers."+c.Label+".topics"), ",")
	c.TopicsRegex = config.GetString(ctx, "kafka.consumers."+c.Label+".topics_regex")

	saramaConfig := sarama.NewConfig()
	saramaConfig.Consumer.Return.Errors = true

	// Set initial offset from config, default to newest if not specified
	offsetInitial := config.GetString(ctx, "kafka.consumers."+c.Label+".offset_initial")
	switch strings.ToLower(offsetInitial) {
	case "oldest":
		saramaConfig.Consumer.Offsets.Initial = sarama.OffsetOldest
	case "newest", "":
		saramaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
	default:
		log.Warn("Invalid offset_initial value, defaulting to 'newest'", zap.String("value", offsetInitial), zap.String("consumer", c.Label))
		saramaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
	}

	saramaConfig.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategySticky
	saramaConfig.Consumer.Fetch.Min = 5242880
	saramaConfig.Consumer.Fetch.Default = 50_000_000
	saramaConfig.Consumer.MaxWaitTime = 1 * time.Second
	saramaConfig.Consumer.MaxProcessingTime = 2 * time.Second

	version := config.GetString(ctx, "kafka.consumers."+c.Label+".version")
	if version == "" {
		saramaConfig.Version = sarama.MaxVersion
	}

	kafkaVersion, err := sarama.ParseKafkaVersion(version)
	if err != nil {
		log.Error("Invalid version supplied for kafka consumer " + version)
	} else {
		saramaConfig.Version = kafkaVersion
	}

	c.config = saramaConfig

	c.validate()
}

func (c *Consumer) validate() {
	if len(c.Brokers) == 0 {
		log.Errorf("No brokers to connect to for kafka consumer")
	}

	if len(c.Topics) == 0 {
		log.Errorf("No topics to listen to for kafka consumer")
	}

	if len(c.ConsumerGroupID) == 0 {
		if len(c.ConsumerGroupID) == 0 {
			log.Errorf("No consumer group specified kafka consumer. Please verify if " + strings.ToUpper("flash_gateway_kafka_consumers_"+c.Label+"_group") + " env is set")
		}
	}
}

func (c *Consumer) setup(ctx context.Context) {
	c.init(ctx)

	client, err := sarama.NewClient(c.Brokers, c.config)
	if err != nil {
		log.Errorf("Error creating client: %v", err)
	} else {
		topics_list, _ := client.Topics()
		for _, topic := range topics_list {
			if c.TopicsRegex != "" {
				if matched, _ := regexp.MatchString(c.TopicsRegex, topic); matched {
					c.Topics = append(c.Topics, topic)
				}
			}
		}
		client.Close()
	}

	consumer, err := sarama.NewConsumerGroup(c.Brokers, c.ConsumerGroupID, c.config)
	if err != nil {
		log.Error("Error occured while creating kafka consumer client", zap.Any("error", err.Error()))
	}

	c.ConsumerGroup = ConsumerGroup{
		Consumer:          consumer,
		ready:             make(chan bool),
		ConsumerClaimFunc: ConsumerClaimFunc,
	}

	go func() { //nolint: ignore-recover
		for err := range c.ConsumerGroup.Consumer.Errors() {
			log.Error("Error from consumer", zap.Any("error", err))
		}
	}()
}

// Consume starts listening on messages
func (c *Consumer) Consume() {
	ctx, cancel := context.WithCancel(context.Background())

	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() { //nolint: ignore-recover
		defer wg.Done()
		for {
			// `Consume` should be called inside an infinite loop, when a
			// server-side rebalance happens, the consumer session will need to be
			// recreated to get the new claims
			if err := c.ConsumerGroup.Consumer.Consume(ctx, c.Topics, &c.ConsumerGroup); err != nil {
				log.Error("Error from consumer when consuming", zap.Any("error", err))
			}
			// check if context was cancelled, signaling that the consumer should stop
			if ctx.Err() != nil {
				return
			}

			c.ConsumerGroup.ready = make(chan bool)
		}
	}()

	<-c.ConsumerGroup.ready // Await till the consumer has been set up

	log.Info("Sarama consumer now consuming message")

	select {
	case <-ctx.Done():
		log.Info("Terminating: context cancelled")
	case <-channels.ShutdownChannel:
		log.Info("kafka: Shutdown channel called")
		break
	}

	cancel()

	wg.Wait()

	if err := c.ConsumerGroup.Consumer.Close(); err != nil {
		log.Warn("Consumer group shutdown unsuccessful", zap.String("error", err.Error()))
	}
	channels.ConsumerShutdownComplete <- true
}
