package kafka

import (
	"context"

	"github.com/Zomato/flash-gateway/internal/constants"
	"github.com/Zomato/flash-gateway/internal/helpers"
	log "github.com/Zomato/go/logger"
	newrelic_agent "github.com/newrelic/go-agent"
)

var (
	consumer Consumer

	// Producers stores the array of all producers
	Producers map[string]Producer

	// DefaultProducer stores producer with default producer
	DefaultProducer Producer

	// ConsumerClaimFunc stores the claim func to be used
	ConsumerClaimFunc ConsumerClaimFuncType
)

// SetupProducers setups producers
func SetupProducers(ctx context.Context, labels []string) {
	if len(labels) == 0 {
		return
	}

	labels = helpers.UniqueStringSlice(labels)
	Producers = RegisterProducers(ctx, labels)
	if len(Producers) == 0 {
		log.Errorf("No producers registered")
	}

	DefaultProducer = Producers[DEFAULT_PRODUCER_NAME]
}

// Start setups consumer and starts consumption
func Start(kafkaConsumerLabel string, ctx context.Context) {
	consumer = NewConsumer(kafkaConsumerLabel, ctx)
	consumer.Consume()
}

// SetConsumerClaimFunc sets claim function to be used when setting up client
func SetConsumerClaimFunc(consumerClaimFunc ConsumerClaimFuncType) {
	ConsumerClaimFunc = consumerClaimFunc
}

// Shutdown shutdowns kafka producers and consumers
func Shutdown() {
	log.Info("Shutting down kafka producers")

	for _, p := range Producers {
		p.Shutdown()
	}
}

func addNewRelicKafkaSegment(ctx context.Context, operation string) newrelic_agent.DatastoreSegment {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)

	segment := newrelic_agent.DatastoreSegment{
		Product:    "Kafka",
		Collection: jobType,
		Operation:  operation,
	}

	if ctx != nil {
		txn := newrelic_agent.FromContext(ctx)
		segment.StartTime = newrelic_agent.StartSegmentNow(txn)
	}

	return segment
}
