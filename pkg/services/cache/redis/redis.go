package redis

import (
	"context"
	"strings"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/redis"

	"go.uber.org/zap"
)

type Client struct {
	Cache *redis.Client
}

// Initialize returns a new redis conn instance
func Initialize(ctx context.Context, metrics bool) *Client {
	hosts := strings.Split(config.GetString(ctx, "cache.l1.hosts"), ",")
	redisConn, err := redis.NewClient(&redis.Config{
		Hosts:                hosts,
		ClusterMode:          config.GetBool(ctx, "cache.l1.clustermode"),
		ServeReadsFromSlaves: config.GetBool(ctx, "cache.l1.servereadsfromslaves"),
		PoolSize:             config.GetUint(ctx, "cache.l1.poolsize"),
	})

	if err != nil {
		log.Error("Could not create redis client", zap.String("hosts", config.GetString(ctx, "cache.l1.hosts")), zap.Any("error", err.Error()))
	}

	_, err = redisConn.Ping(ctx).Result()
	if err != nil {
		log.Error("Could not connect to redis", zap.String("hosts", config.GetString(ctx, "cache.l1.hosts")), zap.Any("error", err.Error()))
	}

	log.Info("Redis connected.")

	return &Client{redisConn}
}

func (c *Client) HGet(ctx context.Context, key, field string) (string, error) {
	return c.Cache.HGet(ctx, key, field)
}

func (c *Client) HMGet(ctx context.Context, key string, fields []string) ([]interface{}, error) {
	return c.Cache.HMGet(ctx, key, fields...)
}

func (c *Client) HMSet(ctx context.Context, key string, values map[string]interface{}) (int64, error) {
	return c.Cache.HMSet(ctx, key, values)
}

func (c *Client) Pipeline() (*Pipeline, error) {
	return &Pipeline{
		pipeliner: c.Cache.Pipeline(),
	}, nil
}

type HMSETPair struct {
	Key    string
	Fields map[string]interface{}
}

func (c *Client) PipedMSet(ctx context.Context, pairs []HMSETPair) error {
	p, err := c.Pipeline()
	if err != nil {
		return err
	}

	for _, pair := range pairs {
		p.HMSet(ctx, pair)
	}

	return p.Exec(ctx)
}
