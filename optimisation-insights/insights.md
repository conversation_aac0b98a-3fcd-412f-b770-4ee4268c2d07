
high throughput topics
- jumbo_streaming.blinkit.jumbo2.impression_events 
    - max msg per min - 4 million
    - 50 partitions 
- jumbo_streaming.blinkit.jumbo2.product_image_shown_events 
    - max msg per min - 3.5 million
    - 50 partitions
- jumbo_streaming.zomato.jumbo2.promo_events 
    - max msg per min - 12 million
    - 10 partitions
- jumbo_streaming.zomato.jumbo2.eta_tracking 
    - max msg per min - 6.5 million
    - 30 partitions
- zomato topics are in custom proto converter


ECS services:
    - prod-blinkitjumbov2eventconsumer = ~50-70 workers
    - prod-jumbov2eventconsumer - <40
    - flash-gateway/internal/handlers/protoconv-custom.go for code ref



problem statement:
- seeing high cpu utilization in prod-blinkitjumbov2eventconsumer as compared to prod-jumbov2eventconsumer even though throughput is very less as compared to zomato topics
- schema of blinkit topics is very complex and nested as compared to zomato payloads
- whenever paritions of mentioned blinkit topics go on same ecs task instance, it's cpu goes up and we drain that task manually which triggers rebalancing and lag comes down immeidately
- we need to identify potential unpotilised parts in code which are taking up too much cpu and how can we optimise it to reduce avg running workers