syntax = "proto3";

package jumbo.eventregistry.blinkit.consumer.productimageshownevents;

option go_package = "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/productimageshownevents";
option php_namespace = "Jumbo\\EventRegistry\\Blinkit\\Consumer\\ProductImageShownEvents";
option php_metadata_namespace = "Jumbo\\EventRegistry\\Metadata\\Blinkit\\Consumer\\ProductImageShownEvents";
option java_multiple_files = true;
option java_package = "com.jumbo.eventregistry.blinkit.consumer.productimageshownevents";
option java_outer_classname = "ProductImageShownEventsProto";
option ruby_package = "Jumbo::EventRegistry::Blinkit::Consumer::ProductImageShownEvents";

import "google/protobuf/wrappers.proto";
import "jumbo/eventregistry/annotations/message_options.proto";

// mobile impression events
message ProductImageShownEvents {
    option (jumbo.eventregistry.annotations.msg_opts).tenant = "BLINKIT";
    option (jumbo.eventregistry.annotations.msg_opts).database = "jumbo2";
    option (jumbo.eventregistry.annotations.msg_opts).table = "product_image_shown_events";
    option (jumbo.eventregistry.annotations.msg_opts).owner = "dataplatform";
    option (jumbo.eventregistry.annotations.msg_opts).email = "<EMAIL>";

    // this specifies the event name
    google.protobuf.StringValue event_name = 1;

    // this specifies the user traits attached to the event
    Traits traits = 2;

    // this specifies the properties attached to the event
    Properties properties = 3;
}

// Schema of Properties attached to the event
message Properties {

    // Products in the order/event
    repeated Products products = 1;

    // Details of shipments present in cart
    repeated Shipments shipments = 2;

    // ID of child meta in which the event happened
    google.protobuf.StringValue child_widget_id = 3;

    // Name of the child widget. E.g. Product Card
    google.protobuf.StringValue child_widget_name = 4;

    // Widget position of child meta in which the event happened
    google.protobuf.Int32Value child_widget_position = 5;

    // Widget revision id of child meta in which the event happened
    google.protobuf.StringValue child_widget_revision_id = 6;

    // Title of child meta in which the event happened
    google.protobuf.StringValue child_widget_title = 7;

    // Tracking id of child meta in which the event happened
    google.protobuf.StringValue child_widget_tracking_id = 8;

    // Widget variation id of child meta in which the event happened
    google.protobuf.StringValue child_widget_variation_id = 9;

    // Usually widget title in case user has opened this page from a widget, else can be any other unique property.
    google.protobuf.StringValue entry_source_title = 10;

    // Is page rendered by React Native
    google.protobuf.BoolValue is_react_page = 11;

    // Last Page Id
    google.protobuf.StringValue last_page_id = 12;

    // Last Page Name
    google.protobuf.StringValue last_page_name = 13;

    // Last Page Title
    google.protobuf.StringValue last_page_title = 14;

    // Last Page session id from which user entered this screen
    google.protobuf.StringValue last_page_visit_id = 15;

    // Sub Page Name in which the event happened
    google.protobuf.StringValue last_sub_page_name = 16;

    // Last Sub Page Title
    google.protobuf.StringValue last_sub_page_title = 17;

    // Last Sub Page ID
    google.protobuf.StringValue last_sub_page_visit_id = 18;

    // Id in which the event happened
    google.protobuf.StringValue page_id = 19;

    // Page Name in which the event happened
    google.protobuf.StringValue page_name = 20;

    // Page revision id in which the event happened, if any
    google.protobuf.StringValue page_revision_id = 21;

    // Page Title in which the event happened
    google.protobuf.StringValue page_title = 22;

    // Page tracking id in which the event happened
    google.protobuf.StringValue page_tracking_id = 23;

    // Page Variation id in which the event happened
    google.protobuf.StringValue page_variation_id = 24;

    // Page session id in which the event happened
    google.protobuf.StringValue page_visit_id = 25;

    // Sub Page Name in which the event happened
    google.protobuf.StringValue sub_page_name = 26;

    // Sub Page Title in which the event happened
    google.protobuf.StringValue sub_page_title = 27;

    // Sub Page Visit id in which the event happened
    google.protobuf.StringValue sub_page_visit_id = 28;

    // Tracking id of the widget
    google.protobuf.StringValue widget_tracking_id = 29;

    // Represent the variation of the information Shown Type of the bottom strip Widget variation id in which the event happened
    google.protobuf.StringValue widget_variation_id = 30;

    // The number of addresses present at the address screen
    google.protobuf.Int32Value address_count = 31;

    // Id of the address selected
    google.protobuf.StringValue address_id = 32;

    // Represent the address type of the user -> myself or someoneElse
    google.protobuf.StringValue address_type = 33;

    // This is the asset type ID of the monet campaign driving the widget
    google.protobuf.StringValue ads_asset_type_id = 34;

    // This is the campaign ID of the monet campaign driving the widget
    google.protobuf.Int32Value ads_campaign_id = 35;

    // This is the collection where the CTA in the ad widget lands
    google.protobuf.StringValue ads_collection_id = 36;

    // This is the subcampaign ID of the monet campaign driving the widget
    google.protobuf.Int32Value ads_subcampaign_id = 37;

    // This is the monet ad type of the monetized widget
    google.protobuf.StringValue ads_type = 38;

    // The amount added for tip/Feeding india donation/payment
    google.protobuf.DoubleValue amount = 39;

    // Sponsored badge of a product
    google.protobuf.StringValue badge = 40;

    // Brand associated with the product
    google.protobuf.StringValue brand = 41;

    // Current campaign associated with the cart/product/deeplink/offer/widget
    google.protobuf.StringValue campaign = 42;

    // Campaign Id of a notification sent from Clevertap
    google.protobuf.StringValue campaign_id = 43;

    // Provide a reward milestone campaign id of the offer (from rewards dynamo table)
    google.protobuf.StringValue campaign_identifier = 44;

    // Type of the product card shown
    google.protobuf.StringValue card_type = 45;

    // ID of the current cart
    google.protobuf.StringValue cart_id = 46;

    // To identify wether the cart is of product/paas or anything else
    google.protobuf.StringValue cart_type = 47;

    // The total amount for items in cart at the time of click
    google.protobuf.DoubleValue cart_value = 48;

    // Collection id of the item shown
    google.protobuf.StringValue collection_id = 49;

    // This refers to the type of CTA used in button on product Type of CTA - Primary / Secondary
    google.protobuf.StringValue cta_type = 50;

    // the cumulative delay occured in the current order state
    google.protobuf.DoubleValue cumulative_delay = 51;

    // Currency code associated with the transaction
    google.protobuf.StringValue currency = 52;

    // cumulative savings on total items currently present in the cart
    google.protobuf.DoubleValue current_cart_savings = 53;

    // API related Metrics/data (Request Metrics/HTTP Error)
    google.protobuf.StringValue custom_data = 54;

    // Deeplink called on cta click
    google.protobuf.StringValue deeplink = 55;

    // current fetched device latitude of customer
    google.protobuf.DoubleValue device_lat = 56;

    // current fetched device longitude of customer
    google.protobuf.DoubleValue device_lon = 57;

    // Whether the button/pill is enabled or not
    google.protobuf.BoolValue enabled = 58;

    // This attribute identifies the button that is clicked
    google.protobuf.StringValue event_source_identifier = 59;

    // state of the favourite icon when event is fired, i.e. liked or unliked
    google.protobuf.StringValue favourite_icon_state = 60;

    // This refers to the badge for the few left tag
    google.protobuf.StringValue few_left_badge = 61;

    // list of filter keys available in the bottom sheet, stored as csv
    google.protobuf.StringValue filter_keys = 62;

    // it contains all filters that are currently applied
    google.protobuf.StringValue filters_present = 63;

    // id of properties
    google.protobuf.StringValue id = 64;

    // This is the url of banner image
    google.protobuf.StringValue image_url = 65;

    // were images shown for that keyword
    google.protobuf.BoolValue images_shown = 66;

    // were images shown for that keyword; csv of flags
    google.protobuf.StringValue images_shown_flags = 67;

    // Invalid ids of the field when this event occured
    repeated google.protobuf.StringValue invalid_ids = 68;

    // Inventory of product, 0 for OOS
    google.protobuf.Int32Value inventory = 69;

    // Inventory limit of the product
    google.protobuf.Int32Value inventory_limit = 70;

    // This refers if offer tag is present on a product
    google.protobuf.BoolValue is_offer = 71;

    // Total items in cart (not unique, not including oos items)
    google.protobuf.Int32Value items_in_cart = 72;

    // L0 Category of the product
    google.protobuf.StringValue l0_category = 73;

    // L1 Category of the product
    google.protobuf.StringValue l1_category = 74;

    // L2 Category of the product
    google.protobuf.StringValue l2_category = 75;

    // Label of the shared address
    google.protobuf.StringValue label = 76;

    // Latitude of the final pin on which address was created
    google.protobuf.DoubleValue latitude = 77;

    // Longitude of the location
    google.protobuf.DoubleValue longitude = 78;

    // The ID of merchant from where the order is placed
    google.protobuf.Int32Value merchant_id = 79;

    // This refers to the type of backend merchant from which the product is served
    google.protobuf.StringValue merchant_type = 80;

    // To know about the message reported in case of error state
    google.protobuf.StringValue message = 81;

    // Maximum Retail Price of the product
    google.protobuf.Int32Value mrp = 82;

    // name of product
    google.protobuf.StringValue name = 83;

    // This is the offer applicable on the category
    google.protobuf.StringValue offer = 84;

    // This refers if offer text is Top Saver or Extra %
    google.protobuf.BoolValue offer_text = 85;

    // Order ID generated when the order is placed
    google.protobuf.StringValue order_id = 86;

    // state of order during the event (Ex: Order Placed, Packing Started, Out for Delivery)
    google.protobuf.StringValue order_state = 87;

    // List of badges shown on product images
    google.protobuf.StringValue overlay_badges = 88;

    // Name of the type (collection or category) of the page viewed
    google.protobuf.StringValue page_type = 89;

    // PID of product for which this alternative is shown
    google.protobuf.StringValue parent_product = 90;

    // The payment mode used to complete the txn
    google.protobuf.StringValue payment_mode = 91;

    // Position of pills shown among all the pills
    google.protobuf.Int32Value position = 92;

    // price of product
    google.protobuf.DoubleValue price = 93;

    // This refers to number of products visible in the bottom sheet number of items number of unavailable items
    google.protobuf.Int32Value product_count = 94;

    // Product id of the item shown
    google.protobuf.Int64Value product_id = 95;

    // List of product IDs selected by user. array of product IDs Id's of products displayed in STS Options
    google.protobuf.StringValue product_ids = 96;

    // collection_id(in case of collections) & L1 ID(in case of PLP) product list identifier on PLP
    google.protobuf.StringValue product_list_id = 97;

    // This refers to the offer tag on a product
    google.protobuf.StringValue product_offers = 98;

    // Position of the product within the product listing, which is different from widget_position
    google.protobuf.Int32Value product_position = 99;

    // promo_identifers of product
    repeated google.protobuf.StringValue promo_identifers = 100;

    // PType of the product
    google.protobuf.StringValue ptype = 101;

    // Quantity of the product
    google.protobuf.Int32Value quantity = 102;

    // Rating selected by user on a rating widget
    google.protobuf.StringValue rating = 103;

    // Reasons for which product is shown
    google.protobuf.StringValue reason = 104;

    // Represents recommendation_id of the recommended products
    google.protobuf.StringValue recommendation_id = 105;

    // This refers to sbc price of the product
    google.protobuf.Int32Value sbc_price = 106;

    // The actual term that was used in the search query for which search results are shown
    google.protobuf.StringValue search_actual_keyword = 107;

    // The keyword typed in search header for which the suggestion is shown
    google.protobuf.StringValue search_input_keyword = 108;

    // Describes the type of search made - history / trending / direct / category name
    google.protobuf.StringValue search_keyword_parent = 109;

    // The type of keyword that is being searched Name of the product in the widget
    google.protobuf.StringValue search_keyword_type = 110;

    // previous query searched by user
    google.protobuf.StringValue search_previous_keyword = 111;

    // The number of products present in the search results
    google.protobuf.Int32Value search_result_count = 112;

    // last keyword user typed on search input
    google.protobuf.StringValue search_user_typed_input_keyword = 113;

    // Name of selected tab
    google.protobuf.StringValue selected_tab = 114;

    // Position of selected tab
    google.protobuf.Int32Value selected_tab_position = 115;

    // Alphanumeric string that contains the info of all the items present in cart.
    google.protobuf.StringValue shared_id = 116;

    // Delivery fee associated with the transaction delivery charges applied
    google.protobuf.DoubleValue shipping = 117;

    // Source of the button
    google.protobuf.StringValue source = 118;

    // marked or unmarked State of toggle button - checked/unchecked
    google.protobuf.StringValue state = 119;

    // sub_page_id of properties
    google.protobuf.StringValue sub_page_id = 120;

    // sub_page_revision_id of properties
    google.protobuf.StringValue sub_page_revision_id = 121;

    // subcategory of the card shown collection sub_cat id(in case of collections) & L2 IDs(in case of PLP)
    google.protobuf.StringValue subcategory_id = 122;

    // subtitle for various events
    google.protobuf.StringValue subtitle = 123;

    // list of suggested keywords when a product is searched
    google.protobuf.StringValue suggested_keywords = 124;

    // Estimated time for order delivery
    google.protobuf.DoubleValue time_to_delivery_in_mins = 125;

    // title of the attribute
    google.protobuf.StringValue title = 126;

    // Total products present in cart
    google.protobuf.Int32Value total_products_in_cart = 127;

    // Count of total items that are shared from the sender
    google.protobuf.Int32Value total_products_in_shared_cart = 128;

    // Total number of shipments present in Cart
    google.protobuf.Int32Value total_shipments_in_cart = 129;

    // Type of properties
    google.protobuf.StringValue type = 130;

    // The count of unique products that are in the cart before the said event was performed
    google.protobuf.Int32Value unique_products_in_cart = 131;

    // URL for various events used for video(s)/image(s)/deeplink(s)/API(s)
    google.protobuf.StringValue url = 132;

    // This is the id of the widget. This won't change for any change made to the widget
    google.protobuf.StringValue widget_id = 133;

    // The count for which this event was fired, use the event with highest count for the final duration analytics
    google.protobuf.Int32Value widget_impression_count = 134;

    // Name of the widget e.g. Product Card, Horizontal Product Widget etc.
    google.protobuf.StringValue widget_name = 135;

    // This is the position of the widget
    google.protobuf.Int32Value widget_position = 136;

    // This id of the widget will change for any edits made to the widget
    google.protobuf.StringValue widget_revision_id = 137;

    // This is the title of the widget
    google.protobuf.StringValue widget_title = 138;

    // This is the cost ID of the ads campaign driving the widget
    google.protobuf.Int32Value ads_cost_id = 139;

    //Product clickable cta types
    google.protobuf.StringValue product_cta_type = 140;

    // This is used to identify sections of a widget
    google.protobuf.StringValue section_tracking_id = 141;

    // This is used to get when this product will be available next
    google.protobuf.StringValue next_available_at = 142;

    // This is used to get Product state
    google.protobuf.StringValue product_state = 143;

    // list of highlights
    google.protobuf.StringValue highlight_ids = 144;

    // group tracking ID for widgets
    google.protobuf.StringValue widget_group_tracking_id = 145;

    // Entry Source group tracking ID for widgets
    google.protobuf.StringValue entry_source_group_tracking_id = 146;

    // This is to track video shown
    google.protobuf.BoolValue video_shown = 147;

    // for identifying eta type of a product
    google.protobuf.StringValue eta_identifier = 148;

    //This is when click on the heart icon of the product card
    google.protobuf.BoolValue wishlist_added = 149;


    // This is when click on the top right selectable icon of the product card
    google.protobuf.BoolValue is_top_right_icon_selected = 150;

    // This is to indicate what top right selectable icon corresponds to
    google.protobuf.StringValue icon_type = 151;
}


// Struct for the user traits (master schema for all traits)
message Traits {
    // app_flavor of user traits
    google.protobuf.StringValue app_flavor = 1;

    // cart_id of user traits
    google.protobuf.StringValue cart_id = 2;

    // chain_id of user traits
    google.protobuf.Int32Value chain_id = 3;

    // channel of user traits
    google.protobuf.StringValue channel = 4;

    // city_id of user traits
    google.protobuf.Int32Value city_id = 5;

    // city_name of user traits
    google.protobuf.StringValue city_name = 6;

    // device_uuid of user traits
    google.protobuf.StringValue device_uuid = 7;

    // install_campaign of user traits
    google.protobuf.StringValue install_campaign = 8;

    // install_source of user traits
    google.protobuf.StringValue install_source = 9;

    // latitude of user traits
    google.protobuf.DoubleValue latitude = 10;

    // lifetime_orders of user traits
    google.protobuf.Int32Value lifetime_orders = 11;

    // longitude of user traits
    google.protobuf.DoubleValue longitude = 12;

    // merchant_id of user traits
    google.protobuf.Int32Value merchant_id = 13;

    // merchant_name of user traits
    google.protobuf.StringValue merchant_name = 14;

    // monthly_orders of user traits
    google.protobuf.DoubleValue monthly_orders = 15;

    // segment_enabled_features of user traits
    repeated google.protobuf.StringValue segment_enabled_features = 16;

    // session_launch_source of user traits
    google.protobuf.StringValue session_launch_source = 17;

    // session_uuid of user traits
    google.protobuf.StringValue session_uuid = 18;

    // total_order_value of user traits
    google.protobuf.DoubleValue total_order_value = 19;

    // user_type of user traits
    google.protobuf.StringValue user_type = 20;

    // app version of app
    google.protobuf.Int64Value app_version_code = 21;

    // user_experiment_buckets for getting user experiment flags
    repeated google.protobuf.StringValue user_experiment_buckets = 22;

    // install_medium of user traits
    google.protobuf.StringValue install_medium = 23;

    // install_referrer of user traits
    google.protobuf.StringValue install_referrer = 24;

    // a boolean value to represent if the user redirected to default merchant
    google.protobuf.BoolValue is_default_merchant = 25;

    // tracking_id of user traits
    google.protobuf.StringValue tracking_id = 26;

    // appsflyer id for the user
    google.protobuf.StringValue appsflyer_app_instance_id = 27;

    // firebase id for the user
    google.protobuf.StringValue firbase_app_instance_id = 28 [deprecated = true];

    // firebase id for the user
    google.protobuf.StringValue firebase_app_instance_id = 29;

    // Location Hex value of user
    google.protobuf.StringValue location_hex = 30;

    // host_app_type
    google.protobuf.StringValue host_app_type = 31;

    // host_app_version
    google.protobuf.StringValue host_app_version = 32;

    // host_app_version_code
    google.protobuf.Int64Value host_app_version_code = 33;

    // host_app_user_id
    google.protobuf.StringValue host_app_user_id = 34;

    // segment_type of user traits
    repeated google.protobuf.StringValue segment_type = 35;
}


// Struct of products inside properties
message Products {
    // product_id of product
    google.protobuf.Int64Value product_id = 1;

    // l0_category of product
    google.protobuf.StringValue l0_category = 2;

    // l1_category of product
    google.protobuf.StringValue l1_category = 3;

    // l2_category of product
    google.protobuf.StringValue l2_category = 4;

    // ptype of product
    google.protobuf.StringValue ptype = 5;

    // name of product
    google.protobuf.StringValue name = 6;

    // brand of product
    google.protobuf.StringValue brand = 7;

    // price of product
    google.protobuf.DoubleValue price = 8;

    // mrp of product
    google.protobuf.DoubleValue mrp = 9;

    // quantity of product
    google.protobuf.Int32Value quantity = 10;

    // type_id of product
    google.protobuf.Int64Value type_id = 11;

    // currency of product
    google.protobuf.StringValue currency = 13;

    // shipment_id of product
    google.protobuf.StringValue shipment_id = 14;

    // inventory_limit of product
    google.protobuf.Int64Value inventory_limit = 15;

    // sbc_price of product
    google.protobuf.DoubleValue sbc_price = 16;
}

// Struct of shipments inside proprties
message Shipments {
    // shipment_id of shipment
    google.protobuf.StringValue shipment_id = 1;

    // shipment_type of shipment
    google.protobuf.StringValue shipment_type = 2;

    // slot_amount of shipment
    google.protobuf.DoubleValue slot_amount = 3;

    // shipment_value of shipment
    google.protobuf.DoubleValue shipment_value = 4;

    // slot_date of shipment
    google.protobuf.StringValue slot_date = 5;

    // slot_time of shipment
    google.protobuf.StringValue slot_time = 6;

    // is_edit_shipment of shipment
    google.protobuf.BoolValue is_edit_shipment = 7;

    // is_earliest_slot of shipment
    google.protobuf.BoolValue is_earliest_slot = 8;
}