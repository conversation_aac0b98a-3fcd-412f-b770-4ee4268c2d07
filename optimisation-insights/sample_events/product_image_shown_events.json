{"app_info": {"app_appearance": "APPEARANCE_DARK", "device_performance": "DEVICE_PERFORMANCE_UNSPECIFIED", "system_theme": "SYSTEM_THEME_UNSPECIFIED", "theme": "APP_THEME_UNSPECIFIED"}, "device_id": "E8CBC5A9-30F1-4C97-BC7F-36E4DDC09F8B", "event_id": "", "event_name": "Image Shown", "ingestion_timestamp": 1756416435, "location": 1, "properties": {"badge": "savings", "brand": "Elle 18", "card_type": "variant-selection", "cart_id": "-1", "child_widget_position": 0, "currency": "INR", "eta_identifier": "express", "filters_present": "relevance;", "highlight_ids": "80,758", "icon_type": "wishlist", "inventory": 2, "inventory_limit": 2, "is_top_right_icon_selected": false, "l0_category": "", "l1_category": "", "l2_category": "Lipsticks", "last_page_id": "155", "last_page_name": "feed", "last_page_title": "feed_default", "last_page_visit_id": "0FEED0F0-7CC3-4D6A-B12D-AE04AEB70A2B", "latitude": 12.954776022061054, "longitude": 77.61588397170428, "merchant_id": 30377, "merchant_type": "express", "mrp": 160, "name": "Elle 18 Matte Liquid Lip Color (<PERSON>ude Pump)", "page_id": "OTg3NjU0MzIxMjM0NTMzNDE=", "page_name": "listing_widgets", "page_title": "Listing", "page_tracking_id": "#-NA", "page_type": "Category", "page_visit_id": "GSplitViewController-1293E119-B0EB-4934-9F66-9B8D61A4C759", "price": 144, "product_id": "499655", "product_list_id": "OTg3NjU0MzIxMjM0NTMzNDE=", "product_offers": "percentage_off", "product_position": 8, "ptype": "Lip Color", "quantity": 1, "rating": "3.716", "state": "available", "subcategory_id": "2411", "time_to_delivery_in_mins": 8, "title": "Elle 18 Matte Liquid Lip Color (<PERSON>ude Pump)", "widget_id": "499655", "widget_impression_count": 1, "widget_name": "Product", "widget_position": 8, "widget_title": "Elle 18 Matte Liquid Lip Color (<PERSON>ude Pump)", "widget_variation_id": "global_product_listing", "wishlist_added": false}, "sequence_id": "", "sequence_offset": 0, "session_id": "C2080F5A-CF06-4E1D-A0C8-248E100B2A6D", "source": "ios", "time": 1756416428, "timestamp": 1756416428, "traits": {"app_flavor": "NORMAL", "app_version_code": "172200", "appsflyer_app_instance_id": "1720444235563-4914629", "chain_id": 1383, "channel": "BLINKIT", "city_id": 3, "city_name": "Bengaluru", "device_uuid": "E8CBC5A9-30F1-4C97-BC7F-36E4DDC09F8B", "firebase_app_instance_id": "816E4B3394804BDFBC49262AAF58EA8D", "latitude": 12.954776022061054, "lifetime_orders": 36, "longitude": 77.61588397170428, "merchant_id": 30377, "monthly_orders": 7, "segment_enabled_features": ["slp-weighted-ranking-equation:0", "show-sort-slp", "ads-service-flow-rollout:baelish", "back-in-stock-grid:enabled", "cbf-similarity-enabled", "cbf-dismiss-button-enabled", "see-all-on-slp", "see-all-recipe-on-plp", "ads-collection-inventory-check-migration", "semantic-search", "slp-3-cards", "autosuggest-keyword-ranking:0", "paas-cart-merge-enabled", "plp-tobacco-consent:enable", "top-brands:3", "pin-lq-products", "pecos", "generated-tags-autosuggest", "continue-browsing-for:enable", "autocomplete-prefix-match-variation:default", "mandatory-autosuggest", "feed-recipe-v2-rollout:enable", "oos-widget-on-slp", "search-show-duplicate-autosuggest", "enable-usecase-container", "search-autosuggest-tap-vs-type:1", "enable-empty-search-v2", "low-converting-keywords-boosting", "city-sales-ranking-v2", "ad-density-variation:0", "product-suggestion", "generated-tag-variation:1", "search-similarity-model", "keyterm-similarity-variation:1", "show-user-query-keyword-autosuggestion", "search-instead-for", "incomplete-words-boosting", "recommendations-variation:1", "variant-selection-pop-up-v2:bottom_sheet_with_tag", "item_rail_v2", "show-images-in-autosuggest", "attribute-personalization:2", "location-info-reverse-geocode-service:sauron", "pdp-recommendation:fetch_related_products_from_espina", "on-demand-merchandising", "use-sales-city-level-data", "should-use-constant-scoring", "use-autosuggest-as-intent", "category-filtering", "use-ner", "autosuggest-incomplete-spellcorrect", "sponsored-banners-v2", "autosuggest-variation:1", "search-models-variations:1", "show-usecases", "keyword-recipe-position-decider:keyword_first", "inline-filters-visibility", "show-similar-products", "user-bucket:C", "category_reorder_on_feed_experiment:2_0_0", "search-within-plp", "buy-more-save-more:b3c61c", "new-er-scoring-logic", "use-sales-data", "affluence-experiment", "autocomplete-variation:0", "use-spellcorrect-model", "sponsored-products-boost:1", "few-left-exp:bf21a9", "use-primary-secondary-merged-flow-in-type-to-search", "category-grid-old-user-rows:5", "plp-instamart:vertical-plp", "type-to-search", "search-keyterm-similarity", "search-city-level-autosuggest", "global-collection", "fastest_delivery_vs_eta:control", "top-seller-nu-city-wise:control_top_seller", "households-served-experiment:control_households", "is-cart-footer-strip-shown", "bill-buster-experiment:billbuster_control", "express-allowed-user-segment", "express-merchant-delivery-threshold:-1", "exp-feed-carousel-with-variations:3000", "plp-promo-banner:bf21a9", "google_maps", "map_my_india", "quick-cart-enabled", "show-dynamic-paan-corner-banner", "show-search-filters", "track-order-unified-rating-experience-rollout", "show-kitchen-tools-rail", "track-order-v-3-enabled", "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag", "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag", "special-groups-for-goa:fd7a20", "smart-basket-version:v1", "intelligent-pb-version:v2", "discover-new-finds:enabled", "dnf-boosting:", "byc-boosting-config:fd30b6", "npr-migration", "dynamic-curated-for-you-rollout", "pdp-similar-items-v2", "npr-version:v8", "rollout-tod-best-sellers", "app-v-16-rollout-enabled", "post-checkout-scratch-card-rollout", "pdp-similar-products-ab", "attribute-rails:3", "cms-filters-rollout", "pin-new-products", "pb-propensity", "post-checkout-ios-type-6-snippet-migration-enabled", "enable_import_z_address_on_cart", "new-user-ranking", "consumer-app-international-phone-login-enabled", "use-query-to-product", "dc-rail", "aspirational-card-rail", "byc-pb-ptype-logic", "consumer-app-pdp-switcher-enabled", "slp-trending-behaviour:3", "empty-search-rollout", "dc-realtime-sorting-config:bf21a9", "cart-master-ptype-based-deboosting-on-npr", "reduce-precision-set-byc", "mv-instant-enabled", "empty-search-trending-keyterms:enabled", "enable-blinkit-money", "dark-mode-rollout-enabled", "high-confident-pb-ranking", "use-butterfly-model", "pb-intelligent-scoring-version:v2", "use-product-sales-score", "new-text-match-qtp-scoring", "is_variant_compression_enabled", "is-primary-config-endpoint-enabled", "post-checkout-crystal-bff-migration-rollout-enabled", "track-order-v2-aerobar-version-rollout", "consumer-app-wishlist-feature-enabled", "butterfly-spellcorrect-ab", "use-butterfly-spellcorrect", "location-permission-popup-v2-enabled", "enable-pharma-flow", "location-autosuggest-backend-enabled", "consumer-app-web-view-auth-token-flow-enabled", "filters_v3", "use-pds-product-id-products", "consumer-app-feeding-india-impact-page-rollout-enabled", "far_away_flow_profile", "far_away_flow_cart"], "segment_type": ["high_aov_segment_dump_new_def", "lux_brands_segment", "baby_care_segment", "electronics_lapsed_segment", "ntc_electronics_and_electricals", "stationary_buyers", "harpic_user", "premium_ull_flag", "segment_hair_care", "health_fitness", "ads_diy_baby_buy", "ads_diy_personal_care_buy", "chocolate_munchies_biscuit_premium", "ads_kids", "repellents_user", "ads_diy_grocery_buy", "ads_female", "plum_potential_user", "razor_premium", "fem_care_user", "ads_diy_beauty_cosmetics_buy", "top_twenty_purchaser", "soulflower_new_potential", "grocery_user", "baby_buyers_6", "sexual_wellness_potential", "beauty_customers", "diswashing_gel_sampling", "personal_care_user", "party_esttential_potential", "batter_cohort", "chicconew_auto", "blr_dro_jigsaw", "babycareuser_cdp", "ASBL_Flyer", "personalisation_premium_user_segment", "navratna_bpc", "personalisation_kids_cohort_high", "Users_Female", "femcare_user_whisper", "personalisation_non_vegetarian_cohort_medium", "batter_event_cohort_blr", "baby<PERSON><PERSON><PERSON>_mamaearth", "beautypersonalcare_cdp", "navratnasampling_cdp", "personalisation_toddler_infant_cohort_high", "mccainrakhiflyer_user", "segment_z_blinkit75_offer", "segment_z_blinkit_coke_offer_v4"], "session_uuid": "C2080F5A-CF06-4E1D-A0C8-248E100B2A6D", "user_experiment_buckets": ["RANDOM#bucket2", "INCREASING_ROLLOUT#bucket2", "DECREASING_ROLLOUT#bucket2"], "user_type": "ACTIVE"}, "url": "Screen Name", "user_agent": "&source=ios&sdk_version=1.0&device_manufacturer=Apple&device_model=iPhone+12+Pro+Max&app_version=17.22.0&version=18.1&network_type=WIFI&lang=en", "user_id": "31442899"}