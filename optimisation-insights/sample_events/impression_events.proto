syntax = "proto3";

package jumbo.eventregistry.blinkit.consumer.impressionevents;

option go_package = "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/impressionevents";
option php_namespace = "Jumbo\\EventRegistry\\Blinkit\\Consumer\\ImpressionEvents";
option php_metadata_namespace = "Jumbo\\EventRegistry\\Metadata\\Blinkit\\Consumer\\ImpressionEvents";
option java_multiple_files = true;
option java_package = "com.jumbo.eventregistry.blinkit.consumer.impressionevents";
option java_outer_classname = "ImpressionEventsProto";
option ruby_package = "Jumbo::EventRegistry::Blinkit::Consumer::ImpressionEvents";

import "google/protobuf/wrappers.proto";
import "jumbo/eventregistry/annotations/message_options.proto";


// mobile impression events
message ImpressionEvents {
    option (jumbo.eventregistry.annotations.msg_opts).tenant = "BLINKIT";
    option (jumbo.eventregistry.annotations.msg_opts).database = "jumbo2";
    option (jumbo.eventregistry.annotations.msg_opts).table = "impression_events";
    option (jumbo.eventregistry.annotations.msg_opts).owner = "dataplatform";
    option (jumbo.eventregistry.annotations.msg_opts).email = "<EMAIL>";

    // this specifies the event name
    google.protobuf.StringValue event_name = 1;

    // this specifies the user traits attached to the event
    Traits traits = 2;

    // this specifies the properties attached to the event
    Properties properties = 3;
}

// Schema of Properties attached to the event
message Properties {

    // Products in the order/event
    repeated Products products = 1;

    // Details of shipments present in cart
    repeated Shipments shipments = 2;

    // ID of the Child Widget in which the event happened
    google.protobuf.StringValue child_widget_id = 3;

    // Name of the child widget. E.g. Product Card Name of child meta in which the event happened
    google.protobuf.StringValue child_widget_name = 4;

    // Position of the child widget inside the parent widget
    google.protobuf.Int32Value child_widget_position = 5;

    // Widget revision id of child meta in which the event happened
    google.protobuf.StringValue child_widget_revision_id = 6;

    // Title of child meta in which the event happened
    google.protobuf.StringValue child_widget_title = 7;

    // Tracking id of child meta in which the event happened
    google.protobuf.StringValue child_widget_tracking_id = 8;

    // Child Widget id of the widget from which user opened this page, null for other scenarios
    google.protobuf.StringValue entry_source_child_id = 9;

    // Name of Child Widget/View upon click of which user landed to this page.
    google.protobuf.StringValue entry_source_child_name = 10;

    // Child Widget position in the child list from which user opened this page, 0 for other scenarios
    google.protobuf.Int32Value entry_source_child_position = 11;

    // Usually Child widget title in case user has opened this page from a widget, else can be any other unique property.
    google.protobuf.StringValue entry_source_child_title = 12;

    // Usually Child widget tracking id in case user has opened this page from a widget, else can be any other unique property.
    google.protobuf.StringValue entry_source_child_tracking_id = 13;

    // Child Widget variation id of the widget from which user opened this page, null for other scenarios
    google.protobuf.StringValue entry_source_child_variation_id = 14;

    // Widget id of the widget from which user opened this page, null for other scenarios entry source for button
    google.protobuf.StringValue entry_source_id = 15;

    // Name of Widget/View upon click of which user landed to this page. Element name that is clicked on the screen
    google.protobuf.StringValue entry_source_name = 16;

    // Widget position in the vertical list from which user opened this page, 0 for other scenarios
    google.protobuf.StringValue entry_source_position = 17;

    // Widget revision id of the widget from which user opened this page, null for other scenarios
    google.protobuf.StringValue entry_source_revision_id = 18;

    // Usually widget title in case user has opened this page from a widget, else can be any other unique property.
    google.protobuf.StringValue entry_source_title = 19;

    // Usually widget tracking id in case user has opened this page from a widget, else can be any other unique property.
    google.protobuf.StringValue entry_source_tracking_id = 20;

    // Widget variation id of the widget from which user opened this page, null for other scenarios
    google.protobuf.StringValue entry_source_variation_id = 21;

    // Is page rendered by React Native
    google.protobuf.BoolValue is_react_page = 22;

    // Last Page Id
    google.protobuf.StringValue last_page_id = 23;

    // Last Page Name
    google.protobuf.StringValue last_page_name = 24;

    // Last Page revision id
    google.protobuf.StringValue last_page_revision_id = 25;

    // Last Page Title
    google.protobuf.StringValue last_page_title = 26;

    // Last Page Tracking id
    google.protobuf.StringValue last_page_tracking_id = 27;

    // Ladt Page Variation id
    google.protobuf.StringValue last_page_variation_id = 28;

    // Last Page session id from which user entered this screen
    google.protobuf.StringValue last_page_visit_id = 29;

    // Sub Page Name in which the event happened
    google.protobuf.StringValue last_sub_page_name = 30;

    // Last Sub Page Title
    google.protobuf.StringValue last_sub_page_title = 31;

    // Last Sub Page ID
    google.protobuf.StringValue last_sub_page_visit_id = 32;

    // Id in which the event happened
    google.protobuf.StringValue page_id = 33;

    // Page Name in which the event happened
    google.protobuf.StringValue page_name = 34;

    // Page revision id in which the event happened, if any
    google.protobuf.StringValue page_revision_id = 35;

    // Page Title in which the event happened
    google.protobuf.StringValue page_title = 36;

    // Page tracking id in which the event happened
    google.protobuf.StringValue page_tracking_id = 37;

    // Page Variation id in which the event happened
    google.protobuf.StringValue page_variation_id = 38;

    // Page session id in which the event happened
    google.protobuf.StringValue page_visit_id = 39;

    // Sub Page Name in which the event happened
    google.protobuf.StringValue sub_page_name = 40;

    // Sub Page Title in which the event happened
    google.protobuf.StringValue sub_page_title = 41;

    // Sub Page Visit id in which the event happened
    google.protobuf.StringValue sub_page_visit_id = 42;

    // Tracking id of the widget
    google.protobuf.StringValue widget_tracking_id = 43;

    // Represent the variation of the information Shown Type of the bottom strip Widget variation id in which the event happened
    google.protobuf.StringValue widget_variation_id = 44;

    // The number of addresses present at the address screen
    google.protobuf.Int32Value address_count = 45;

    // Id of the address selected
    google.protobuf.StringValue address_id = 46;

    // Represent the address type of the user -> myself or someoneElse
    google.protobuf.StringValue address_type = 47;

    // This is the asset type ID of the monet campaign driving the widget
    google.protobuf.StringValue ads_asset_type_id = 48;

    // This is the campaign ID of the monet campaign driving the widget
    google.protobuf.Int32Value ads_campaign_id = 49;

    // This is the collection where the CTA in the ad widget lands
    google.protobuf.StringValue ads_collection_id = 50;

    // This is the subcampaign ID of the monet campaign driving the widget
    google.protobuf.Int32Value ads_subcampaign_id = 51;

    // This is the sub type of the widget used to identify monet asset type
    google.protobuf.StringValue ads_type = 52;

    // Unique identifier of aerobar
    google.protobuf.StringValue aerobar_id = 53;

    // The amount added for tip/Feeding india donation/payment
    google.protobuf.DoubleValue amount = 54;

    // comma seperated list of apps installed
    google.protobuf.StringValue app_installed = 55;

    // The available update version code shown to user
    google.protobuf.StringValue available_update_version = 56;

    // Represents the name of the badge shown in shipment or Sponsored badge of a product
    google.protobuf.StringValue badge = 57;

    // Brand associated with the product
    google.protobuf.StringValue brand = 58;

    // Text rendered on the button
    google.protobuf.StringValue button_text = 59;

    // Current campaign associated with the cart/product/deeplink/offer/widget
    google.protobuf.StringValue campaign = 60;

    // Campaign Id of a notification sent from Clevertap
    google.protobuf.StringValue campaign_id = 61;

    // Provide a reward milestone campaign id of the offer (from rewards dynamo table)
    google.protobuf.StringValue campaign_identifier = 62;

    // Type of the product card shown This refers to the type of product card being used
    google.protobuf.StringValue card_type = 63;

    // ID of the current cart
    google.protobuf.StringValue cart_id = 64;

    // products discount of the current cart
    google.protobuf.DoubleValue cart_savings = 65;

    // To identify wether the cart is of product/paas or anything else
    google.protobuf.StringValue cart_type = 66;

    // The total amount for items in cart at the time of click
    google.protobuf.DoubleValue cart_value = 67;

    // The tip is saved for future orders
    google.protobuf.BoolValue checkbox_state = 68;

    // iteration count of child_widget impression
    google.protobuf.Int32Value child_widget_impression_count = 69;

    // contains name of the cta where click came from
    google.protobuf.StringValue click_source = 70;

    // Collection id of the item
    google.protobuf.StringValue collection_id = 71;

    // Amount of the cashback or discount offered
    google.protobuf.DoubleValue coupon_amount = 72;

    // Represents the coupon code
    google.protobuf.StringValue coupon_code = 73;

    // Coupon discount value in the cart
    google.protobuf.DoubleValue coupon_discount_value = 74;

    // Type of the coupon code
    google.protobuf.StringValue coupon_type = 75;

    // This refers to the type of CTA used in button on product or Type of CTA - Primary / Secondary
    google.protobuf.StringValue cta_type = 76;

    // the cumulative delay occured in the current order state
    google.protobuf.DoubleValue cumulative_delay = 77;

    // Currency code associated with the transaction
    google.protobuf.StringValue currency = 78;

    // cumulative savings on total items currently present in the cart
    google.protobuf.DoubleValue current_cart_savings = 79;

    // Represent the id of visible category
    google.protobuf.StringValue current_object_id = 80;

    // API related Metrics/data (Request Metrics/HTTP Error)
    google.protobuf.StringValue custom_data = 81;

    // generic data field 1
    google.protobuf.StringValue data1 = 82;

    // generic data field 2
    google.protobuf.StringValue data2 = 83;

    // generic data field 3
    google.protobuf.StringValue data3 = 84;

    // generic data field 4
    google.protobuf.StringValue data4 = 85;

    // Deeplink called on cta click
    google.protobuf.StringValue deeplink = 86;

    // current fetched device latitude of customer
    google.protobuf.DoubleValue device_lat = 87;

    // current fetched device longitude of customer
    google.protobuf.DoubleValue device_lon = 88;

    // represents the type of dialog 
    google.protobuf.StringValue dialog_type = 89;

    // Whether the button/pill is enabled or not
    google.protobuf.BoolValue enabled = 90;

    // Description of the Http Error Event
    google.protobuf.StringValue error_description = 91;

    // This attribute identifies the button that is clicked
    google.protobuf.StringValue event_source_identifier = 92;

    // state of the favourite icon when event is fired, i.e. liked or unliked
    google.protobuf.StringValue favourite_icon_state = 93;

    // list of filter keys available in the bottom sheet, stored as csv
    google.protobuf.StringValue filter_keys = 94;

    // Position of the filter
    google.protobuf.StringValue filter_position = 95;

    // it contains all filters that are currently applied
    google.protobuf.StringValue filters_present = 96;

    // Icon in overlay clicked (MAXIMIZE, MINIMIZE, CLOSE)
    google.protobuf.StringValue icon = 97;

    // id of properties
    google.protobuf.StringValue id = 98;

    // This is the url of banner image
    google.protobuf.StringValue image_url = 99;

    // were images shown for that keyword
    google.protobuf.BoolValue images_shown = 100;

    // were images shown for that keyword; csv of flags
    google.protobuf.StringValue images_shown_flags = 101;

    // Invalid ids of the field when this event occured
    repeated google.protobuf.StringValue invalid_ids = 102;

    // Inventory of product, 0 for OOS
    google.protobuf.Int32Value inventory = 103;

    // Inventory limit of the product
    google.protobuf.Int32Value inventory_limit = 104;

    // checkbox state item is checked or unchecked
    google.protobuf.BoolValue is_checked = 105;

    // Shows whether the coupon is applicable or not.
    google.protobuf.StringValue is_coupon_applicable = 106;

    // Represents if the selected slot is earliest slot
    google.protobuf.BoolValue is_earliest_slot = 107;

    // Total items in cart (not unique, not including oos items)
    google.protobuf.Int32Value items_in_cart = 108;

    // L0 Category of the product
    google.protobuf.StringValue l0_category = 109;

    // L1 Category of the product
    google.protobuf.StringValue l1_category = 110;

    // L2 category id
    google.protobuf.StringValue l2_category = 111;

    // Represents the current selected label
    google.protobuf.StringValue label = 112;

    // latitiude of the user
    google.protobuf.DoubleValue latitude = 113;

    // Accuracy of the location fetched
    google.protobuf.StringValue location_fetch_accuracy = 114;

    // Altitude of the location fetched
    google.protobuf.StringValue location_fetch_altitude = 115;

    // Way of fetching location - ONE_SHOT or REGULAR
    google.protobuf.StringValue location_update_type = 116;

    // longitude of the user
    google.protobuf.DoubleValue longitude = 117;

    // The ID of merchant from where the order is placed
    google.protobuf.Int32Value merchant_id = 118;

    // This refers to the type of backend merchant from which the product is served
    google.protobuf.StringValue merchant_type = 119;

    // To know about the message reported in case of error state
    google.protobuf.StringValue message = 120;

    // Maximum Retail Price of the product
    google.protobuf.Int32Value mrp = 121;

    // Name of Property
    google.protobuf.StringValue name = 122;

    // The new locality user has switched location to
    google.protobuf.StringValue new_locality = 123;

    // Represent the id of selected/swiped category
    google.protobuf.StringValue next_object_id = 124;

    // Type of a notification sent from Clevertap. Its value would be Push/Email/Web Push/In-app
    google.protobuf.StringValue notification_type = 125;

    // represents the number of files uploaded for PAAS
    google.protobuf.Int32Value number_of_files = 126;

    // Total number of products associated with the pills shown
    google.protobuf.Int32Value number_of_products = 127;

    // Represent the offer whose communication is shown
    google.protobuf.StringValue offer = 128;

    // This refers if offer text is Top Saver or Extra %
    google.protobuf.BoolValue offer_text = 129;

    // The old locality user has switched location from
    google.protobuf.StringValue old_locality = 130;

    // Comma separated string list of product ids
    repeated google.protobuf.StringValue oos_pid_list = 131;

    // Number of active orders in order
    google.protobuf.Int32Value order_count = 132;

    // Hash of Order ID which is shown to customer
    google.protobuf.StringValue order_hash_id = 133;

    // Order ID generated when the order is placed
    google.protobuf.StringValue order_id = 134;

    // state of order during the event (Ex: Order Placed, Packing Started, Out for Delivery)
    google.protobuf.StringValue order_state = 135;

    // status of order visible to user
    google.protobuf.StringValue order_status = 136;

    // List of badges shown on product images
    google.protobuf.StringValue overlay_badges = 137;

    // overlay_present of properties
    google.protobuf.BoolValue overlay_present = 138;

    // Name of the type (collection or category) of the page viewed
    google.protobuf.StringValue page_type = 139;

    // Represents the payload of the notification sent
    google.protobuf.StringValue payload = 140;

    // Payment method selected by the user
    google.protobuf.StringValue payment_method = 141;

    // The payment mode used to complete the txn
    google.protobuf.StringValue payment_mode = 142;

    // Type of popup shown to user
    google.protobuf.StringValue popup_type = 143;

    // Position of pills shown among all the pills
    google.protobuf.Int32Value position = 144;

    // Latitude of the new pin location
    google.protobuf.StringValue post_change_latitude = 145;

    // Longitude of the new pin location
    google.protobuf.StringValue post_change_longitude = 146;

    // Represents the current label before selection
    google.protobuf.StringValue prev_label = 147;

    // Price of the product
    google.protobuf.DoubleValue price = 148;

    // mapping of Product and its alternatives
    repeated ProductAlternatives product_alternatives_list = 149;

    // The number of products present in the search results
    google.protobuf.Int32Value product_count = 150;

    // Product id of the item shown/clicked
    google.protobuf.Int64Value product_id = 151;

    // List of product IDs selected by user
    google.protobuf.StringValue product_ids = 152;

    // collection_id(in case of collections) & L1 ID(in case of PLP) product list identifier on PLP
    google.protobuf.StringValue product_list_id = 153;

    // Position of the product within the product listing, which is different from widget_position
    google.protobuf.Int32Value product_position = 154;

    // ProductIds of the products present in shipment
    repeated ProductsInShipment products_in_shipment = 155;

    // promo_identifers of product
    repeated google.protobuf.StringValue promo_identifers = 156;

    // PType of the product
    google.protobuf.StringValue ptype = 157;

    // Quantity of the product This field tells the packaging quantity
    google.protobuf.Int32Value quantity = 158;

    // Rating selected by user on a rating widget Rating given to order by user rating given by the user
    google.protobuf.StringValue rating = 159;

    // Reasons for which product is shown or Reason of failure of the coupon
    google.protobuf.StringValue reason = 160;

    // Represents recommendation_id of the recommended products
    google.protobuf.StringValue recommendation_id = 161;

    // sbc discount value of the current cart
    google.protobuf.DoubleValue sbc_cart_savings = 162;

    // Represents the payments sdk version used
    google.protobuf.StringValue sdk_version = 163;

    // The actual search term the keyword typed by the user
    google.protobuf.StringValue search_actual_keyword = 164;

    // Keyword manually entered by the user into the search bar, even if partial
    google.protobuf.StringValue search_input_keyword = 165;

    // Describes the type of search made - history / trending / direct / category name
    google.protobuf.StringValue search_keyword_parent = 166;

    // The type of keyword that is being searched Name of the product in the widget
    google.protobuf.StringValue search_keyword_type = 167;

    // previous query searched by user
    google.protobuf.StringValue search_previous_keyword = 168;

    // The number of products present in the search results
    google.protobuf.Int32Value search_result_count = 169;

    // last keyword user typed on search input
    google.protobuf.StringValue search_user_typed_input_keyword = 170;

    // Describes whether the option was selected or deselected
    google.protobuf.BoolValue selected = 171;

    // represents the selected delivery instructions
    google.protobuf.StringValue selected_items = 172;

    // Name of selected tab
    google.protobuf.StringValue selected_tab = 173;

    // Represents the Service Type Used
    google.protobuf.StringValue service_type = 174;

    // Alphanumeric string that contains the info of all the items present in cart.
    google.protobuf.StringValue shared_id = 175;

    // The shipment id of the shipment for which delivery slot page is visited
    google.protobuf.StringValue shipment_id = 176;

    // The shipment type of the shipment for which delivery slot page is visited
    google.protobuf.StringValue shipment_type = 177;

    // Total amount of items present in shipment
    google.protobuf.DoubleValue shipment_value = 178;

    // Delivery fee for the order delivery charge in current cart Delivery fee associated with the transaction delivery charges applied
    google.protobuf.DoubleValue shipping = 179;

    // Selected slot amount This field has 2 uses. Indicate if the selected slot has some cashback running and Indicate if the selected slot is a chargeable slot
    google.protobuf.DoubleValue slot_amount = 180;

    // selected slot Date in the format dd-mm-YYYY
    google.protobuf.StringValue slot_date = 181;

    // Time range of the slot selected for delivery in the format: 5 PM - 8 PM
    google.protobuf.StringValue slot_time = 182;

    // Source of the button
    google.protobuf.StringValue source = 183;

    // SSID of wifi 
    google.protobuf.StringValue ssid = 184;

    // marked or unmarked State of toggle button - checked/unchecked
    google.protobuf.StringValue state = 185;

    // status of properties
    google.protobuf.StringValue status = 186;

    // Sub Page Id in which the event happened
    google.protobuf.StringValue sub_page_id = 187;

    // Sub Page revision id in which the event happened, if any
    google.protobuf.StringValue sub_page_revision_id = 188;

    // subcategory of the card shown collection sub_cat id(in case of collections) & L2 IDs(in case of PLP)
    google.protobuf.StringValue subcategory_id = 189;

    // subtitle for various events
    google.protobuf.StringValue subtitle = 190;

    // Represents the subtitle tag shown in the promise time widget
    google.protobuf.StringValue subtitle_tag = 191;

    // list of suggested keywords when a product is searched
    google.protobuf.StringValue suggested_keywords = 192;

    // The position of the suggestion shown
    google.protobuf.StringValue suggestion_position = 193;

    // The source of the suggestion like (autocomplete, autosuggestion) 
    google.protobuf.StringValue suggestion_source = 194;

    // The type of the suggestion shown like(trending,history,category,subcategory)
    google.protobuf.StringValue suggestion_type = 195;

    // The value of the suggestion shown to the user
    google.protobuf.StringValue suggestion_value = 196;

    // Tells the direction on Which user have swiped (Can be up or down)
    google.protobuf.StringValue swipe_direction = 197;

    // Estimated time for order delivery
    google.protobuf.DoubleValue time_to_delivery_in_mins = 198;

    // tip amount added/removed in cart
    google.protobuf.DoubleValue tip_amount = 199;

    // title of the attribute
    google.protobuf.StringValue title = 200;

    // Total amount paid
    google.protobuf.DoubleValue total = 201;

    // Total number of pills shown
    google.protobuf.Int32Value total_items = 202;

    // The count of items in cart at the time of event
    google.protobuf.Int32Value total_products_in_cart = 203;

    // Count of total items that are shared from the sender
    google.protobuf.Int32Value total_products_in_shared_cart = 204;

    // Total products present in shipment
    google.protobuf.Int32Value total_products_in_shipment = 205;

    // Count of total products that are out of stock
    google.protobuf.Int32Value total_products_out_of_stock = 206;

    // Total number of shipments present in Cart
    google.protobuf.Int32Value total_shipments_in_cart = 207;

    // Type of properties
    google.protobuf.StringValue type = 208;

    // number of unique product in the slider product widget
    google.protobuf.Int32Value unique_products = 209;

    // The count of unique products that are in the cart before the said event was performed
    google.protobuf.Int32Value unique_products_in_cart = 210;

    // The count of unique products that are in the shipment
    google.protobuf.Int32Value unique_products_in_shipment = 211;

    // Count of total unique products that are out of stock
    google.protobuf.Int32Value unique_products_out_of_stock = 212;

    // URL for various events used for video(s)/image(s)/deeplink(s)/API(s)
    google.protobuf.StringValue url = 213;

    // This is the id of the widget. This won't change for any change made to the widget
    google.protobuf.StringValue widget_id = 214;

    // The count for which this event was fired, use the event with highest count for the final duration analytics
    google.protobuf.Int32Value widget_impression_count = 215;

    // Name of the widget e.g. Product Card, Horizontal Product Widget etc.
    google.protobuf.StringValue widget_name = 216;

    // This is the position of the widget
    google.protobuf.Int32Value widget_position = 217;

    // This id of the widget will change for any edits made to the widget
    google.protobuf.StringValue widget_revision_id = 218;

    // This is the title of the widget
    google.protobuf.StringValue widget_title = 219;

    // Type of widget clicked Represents the unique id of the widget
    google.protobuf.Int32Value widget_type = 220;

    // This is the cost ID of the ads campaign driving the widget
    google.protobuf.Int32Value ads_cost_id = 221;

    // merchant_status for tracking
    repeated Merchant merchants = 222;

    // list of out_of_stock products that are not available for shipment
    repeated OOSProduct oos_products = 223;
    
    // This is used to identify the sub-offer which belong to an offer which has multiple sub offers
    google.protobuf.StringValue offer_code = 224;

    // This is used to identify sections of a widget
    google.protobuf.StringValue section_tracking_id = 225;

    // This is used to get when this product will be available next
    google.protobuf.StringValue next_available_at = 226;

    // Feeding india donation
    google.protobuf.BoolValue is_donation_given = 227;

    // This is used to get Product state
    google.protobuf.StringValue product_state = 228;

    // Last Sub Page Id in which the event happened
    google.protobuf.StringValue last_sub_page_id = 229;

    // watch duration of video
    google.protobuf.DoubleValue watched_duration = 230;

    // duration of video
    google.protobuf.DoubleValue duration = 231;

    // is surprise order
    google.protobuf.BoolValue is_surprise_order = 232;
    
    // is surprise order banner hidden or not
    google.protobuf.BoolValue is_items_hidden = 233;

    // list of highlights
    google.protobuf.StringValue highlight_ids = 234;

    // group tracking ID for widgets
    google.protobuf.StringValue widget_group_tracking_id = 235;

    // Entry Source group tracking ID for widgets
    google.protobuf.StringValue entry_source_group_tracking_id = 236;

    // This is to track video shown
    google.protobuf.BoolValue video_shown = 237;

    // is otp detected on user device
    google.protobuf.BoolValue is_otp_detected = 238;

    // input type for phone number field i.e auto or manual
    InputType input_type = 239;

    // This is for birthday for birth date
    google.protobuf.StringValue birth_date = 240;

    // This is for birthday preferences
    google.protobuf.StringValue preference = 241;

    // This is to track if bottom sheet is shown
    google.protobuf.BoolValue is_bottom_sheet_currently_shown = 242;

    // This is to track if bottom sheet was shown before
    google.protobuf.BoolValue has_bottom_sheet_been_shown_before = 243;

    // This is to track if address was selected manually
    google.protobuf.BoolValue is_address_selected_manually = 244;

    // This is to track distance value
    google.protobuf.Int32Value distance = 245;

    //This is used for scratch card
    google.protobuf.StringValue coupon_id = 246;

    // Type of the error
    google.protobuf.StringValue error_type = 247;

    // Error message shown
    google.protobuf.StringValue error_message = 248;

    // for identifying eta type of a product
    google.protobuf.StringValue eta_identifier = 249;

    //This is when click on the heart icon of the product card
    google.protobuf.BoolValue wishlist_added = 250;

    // The login method used to sign in the application
    google.protobuf.StringValue login_method = 251;

    //This method is used for low power mode
    google.protobuf.BoolValue is_low_power_mode = 252;

    //This method is used for accessibility
    google.protobuf.BoolValue is_accessibility_enabled = 253;
}

// Struct for the user traits (master schema for all traits)
message Traits {
    // app_flavor of user traits
    google.protobuf.StringValue app_flavor = 1;

    // cart_id of user traits
    google.protobuf.StringValue cart_id = 2;

    // chain_id of user traits
    google.protobuf.Int32Value chain_id = 3;

    // channel of user traits
    google.protobuf.StringValue channel = 4;

    // city_id of user traits
    google.protobuf.Int32Value city_id = 5;

    // city_name of user traits
    google.protobuf.StringValue city_name = 6;

    // device_uuid of user traits
    google.protobuf.StringValue device_uuid = 7;

    // install_campaign of user traits
    google.protobuf.StringValue install_campaign = 8;

    // install_source of user traits
    google.protobuf.StringValue install_source = 9;

    // latitude of user traits
    google.protobuf.DoubleValue latitude = 10;

    // lifetime_orders of user traits
    google.protobuf.Int32Value lifetime_orders = 11;

    // longitude of user traits
    google.protobuf.DoubleValue longitude = 12;

    // merchant_id of user traits
    google.protobuf.Int32Value merchant_id = 13;

    // merchant_name of user traits
    google.protobuf.StringValue merchant_name = 14;

    // monthly_orders of user traits
    google.protobuf.DoubleValue monthly_orders = 15;

    // segment_enabled_features of user traits
    repeated google.protobuf.StringValue segment_enabled_features = 16;

    // session_launch_source of user traits
    google.protobuf.StringValue session_launch_source = 17;

    // session_uuid of user traits
    google.protobuf.StringValue session_uuid = 18;

    // total_order_value of user traits
    google.protobuf.DoubleValue total_order_value = 19;

    // user_type of user traits
    google.protobuf.StringValue user_type = 20;

    // app version of app
    google.protobuf.Int64Value app_version_code = 21;

    // user_experiment_buckets for getting user experiment flags
    repeated google.protobuf.StringValue user_experiment_buckets = 22;

    // install_medium of user traits
    google.protobuf.StringValue install_medium = 23;

    // install_referrer of user traits
    google.protobuf.StringValue install_referrer = 24;

    // a boolean value to represent if the user redirected to default merchant
    google.protobuf.BoolValue is_default_merchant = 25;

    // tracking_id of user traits 
    google.protobuf.StringValue tracking_id = 26;

    // appsflyer id for the user
    google.protobuf.StringValue appsflyer_app_instance_id = 27;

    // Location Hex value of user
    google.protobuf.StringValue location_hex = 28;

    // host_app_type
    google.protobuf.StringValue host_app_type = 29;

    // host_app_version
    google.protobuf.StringValue host_app_version = 30;

    // host_app_version_code
    google.protobuf.Int64Value host_app_version_code = 31;

    // host_app_user_id
    google.protobuf.StringValue host_app_user_id = 32;

    // segment_type of user traits
    repeated google.protobuf.StringValue segment_type = 33;
}

// Struct for list of products in shipment
message ProductsInShipment {
    // product_id of product
    google.protobuf.Int64Value product_id = 1;
}

// Struct of products inside properties
message Products {
    // product_id of product
    google.protobuf.Int64Value product_id = 1;

    // l0_category of product
    google.protobuf.StringValue l0_category = 2;

    // l1_category of product
    google.protobuf.StringValue l1_category = 3;

    // l2_category of product
    google.protobuf.StringValue l2_category = 4;

    // ptype of product
    google.protobuf.StringValue ptype = 5;

    // name of product
    google.protobuf.StringValue name = 6;

    // brand of product
    google.protobuf.StringValue brand = 7;

    // price of product
    google.protobuf.DoubleValue price = 8;

    // mrp of product
    google.protobuf.DoubleValue mrp = 9;

    // quantity of product
    google.protobuf.Int32Value quantity = 10;

    // type_id of product
    google.protobuf.Int64Value type_id = 11;

    // currency of product
    google.protobuf.StringValue currency = 13;

    // shipment_id of product
    google.protobuf.StringValue shipment_id = 14;

    // inventory_limit of product
    google.protobuf.Int64Value inventory_limit = 15;

    // sbc_price of product
    google.protobuf.DoubleValue sbc_price = 16;
}

// Struct of shipments inside proprties
message Shipments {
    // shipment_id of shipment
    google.protobuf.StringValue shipment_id = 1;

    // shipment_type of shipment
    google.protobuf.StringValue shipment_type = 2;

    // slot_amount of shipment
    google.protobuf.DoubleValue slot_amount = 3;

    // shipment_value of shipment
    google.protobuf.DoubleValue shipment_value = 4;

    // slot_date of shipment
    google.protobuf.StringValue slot_date = 5;

    // slot_time of shipment
    google.protobuf.StringValue slot_time = 6;

    // is_edit_shipment of shipment
    google.protobuf.BoolValue is_edit_shipment = 7;

    // is_earliest_slot of shipment
    google.protobuf.BoolValue is_earliest_slot = 8;
}

// Struct for shown product alternatives
message ProductAlternatives {

    // product_id of product for which we are showing recommendations
    google.protobuf.StringValue pid = 1;

    // list of recommended product_ids
    repeated google.protobuf.StringValue alternatives = 2;
}

// Struct for the merchant_status inside properties
message Merchant {
    // serviceability_status of merchant
    google.protobuf.BoolValue is_serviceable = 1;

    // merchant_id of merchant
    google.protobuf.Int32Value merchant_id = 2;

    // reason_code regarding serviceability of merchant
    google.protobuf.StringValue serviceability_reason = 3;

    // product_ids under merchant
    repeated google.protobuf.Int64Value product_ids = 4;

    // assortment_tag of merchant
    google.protobuf.StringValue assortment_tag = 5;
}

// Struct for out_of_stock products inside properties
message OOSProduct {

    // reason of having product as out of stock
    google.protobuf.StringValue reason = 1;

    // id of product which is out of stock
    google.protobuf.Int64Value id = 2;
}

// EventName denotes the type of input type
enum InputType {
    // INPUT_TYPE_UNSPECIFIED is default event type
    INPUT_TYPE_UNSPECIFIED = 0;
    // when input type is entered automatically
    AUTO = 1;
    // when input type is entered manually
    MANUAL = 2;
}