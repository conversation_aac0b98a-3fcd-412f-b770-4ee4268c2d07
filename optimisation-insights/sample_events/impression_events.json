{"app_info": {"app_appearance": "APPEARANCE_LIGHT", "device_performance": "DEVICE_PERFORMANCE_UNSPECIFIED", "system_theme": "SYSTEM_THEME_DARK", "theme": "APP_THEME_UNSPECIFIED"}, "device_id": "165d6f4d-9d48-4bf9-8ef9-0451323a2fe2", "event_id": "", "event_name": "Search Suggestion Shown", "ingestion_timestamp": 1756415016, "location": 2060, "properties": {"cart_id": "#-NA", "filters_present": "", "images_shown": true, "latitude": 23.3775243, "longitude": 85.3491907, "merchant_id": 36365, "page_id": "search_layout", "page_name": "search", "page_revision_id": "", "page_type": "Global Search", "page_visit_id": "search-92df117f-05ac-414a-b80a-53500a7bf6bc", "search_actual_keyword": "horli", "search_input_keyword": "horli", "search_keyword_parent": "type_to_search", "search_keyword_type": "type_to_search", "search_previous_keyword": "", "search_result_count": 13, "suggestion_source": "autocomplete", "suggestion_type": "composite_keyword", "suggestion_value": "horlicks drink mix", "widget_impression_count": 1, "widget_name": "Suggested Keywords Widget", "widget_position": 5, "widget_title": "Auto Suggestion"}, "sequence_id": "172608", "sequence_offset": 0, "session_id": "2981a670-359b-4a48-960b-f2cf166330c11756415003", "source": "android", "time": 1756415013, "timestamp": 1756415013, "traits": {"app_flavor": "normal", "app_version_code": "80170470", "appsflyer_app_instance_id": "1732604714008-7178420828430001747", "cart_id": "#-NA", "chain_id": 1383, "city_id": 2060, "city_name": "Ranchi", "device_uuid": "90b8c64a7c5fbb45", "install_campaign": "#-NA", "install_medium": "#-NA", "install_referrer": "#-NA", "install_source": "#-NA", "latitude": 23.3775243, "lifetime_orders": 16, "longitude": 85.3491907, "merchant_id": 36365, "monthly_orders": 2, "segment_enabled_features": ["slp-weighted-ranking-equation:0", "show-sort-slp", "ads-service-flow-rollout:baelish", "back-in-stock-grid:enabled", "cbf-similarity-enabled", "cbf-dismiss-button-enabled", "see-all-on-slp", "see-all-recipe-on-plp", "ads-collection-inventory-check-migration", "semantic-search", "slp-3-cards", "autosuggest-keyword-ranking:3", "paas-cart-merge-enabled", "plp-tobacco-consent:disable", "top-brands:3", "pin-lq-products", "pecos", "generated-tags-autosuggest", "continue-browsing-for:enable", "autocomplete-prefix-match-variation:default", "mandatory-autosuggest", "feed-recipe-v2-rollout:enable", "oos-widget-on-slp", "search-show-duplicate-autosuggest", "enable-usecase-container", "search-autosuggest-tap-vs-type:1", "enable-empty-search-v2", "low-converting-keywords-boosting", "city-sales-ranking-v2", "ad-density-variation:0", "product-suggestion", "generated-tag-variation:1", "search-similarity-model", "keyterm-similarity-variation:1", "show-user-query-keyword-autosuggestion", "search-instead-for", "incomplete-words-boosting", "recommendations-variation:1", "variant-selection-pop-up-v2:bottom_sheet_with_tag", "item_rail_v2", "show-images-in-autosuggest", "attribute-personalization:2", "location-info-reverse-geocode-service:sauron", "pdp-recommendation:fetch_related_products_from_espina", "on-demand-merchandising", "should-use-constant-scoring", "use-autosuggest-as-intent", "category-filtering", "use-ner", "autosuggest-incomplete-spellcorrect", "sponsored-banners-v2", "autosuggest-variation:1", "search-models-variations:1", "show-usecases", "keyword-recipe-position-decider:keyword_first", "inline-filters-visibility", "show-similar-products", "user-bucket:B", "category_reorder_on_feed_experiment:2_0_0", "search-within-plp", "buy-more-save-more:54da0c", "new-er-scoring-logic", "use-sales-data", "affluence-experiment", "autocomplete-variation:0", "use-spellcorrect-model", "sponsored-products-boost:1", "few-left-exp:bf21a9", "use-primary-secondary-merged-flow-in-type-to-search", "category-grid-old-user-rows:2", "zomato_onesupport_chat", "plp-instamart:vertical-plp", "type-to-search", "search-keyterm-similarity", "search-city-level-autosuggest", "global-collection", "fastest_delivery_vs_eta:control", "top-seller-nu-city-wise:control_top_seller", "households-served-experiment:control_households", "is-cart-footer-strip-shown", "bill-buster-experiment:billbuster_control", "express-allowed-user-segment", "express-merchant-delivery-threshold:-1", "exp-feed-carousel-with-variations:3000", "plp-promo-banner:bf21a9", "google_maps", "map_my_india", "quick-cart-enabled", "instant-cart", "show-dynamic-paan-corner-banner", "show-search-filters", "instant-cart", "track-order-unified-rating-experience-rollout", "show-kitchen-tools-rail", "track-order-v-3-enabled", "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag", "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag", "special-groups-for-goa:fd7a20", "smart-basket-version:v1", "intelligent-pb-version:v2", "discover-new-finds:enabled", "dnf-boosting:", "byc-boosting-config:fd30b6", "npr-migration", "dynamic-curated-for-you-rollout", "pdp-similar-items-v2", "npr-version:v8", "rollout-tod-best-sellers", "app-v-16-rollout-enabled", "post-checkout-scratch-card-rollout", "pdp-similar-products-ab", "attribute-rails:3", "cms-filters-rollout", "pin-new-products", "pb-propensity", "enable_import_z_address_on_cart", "new-user-ranking", "consumer-app-international-phone-login-enabled", "use-query-to-product", "dc-rail", "aspirational-card-rail", "byc-pb-ptype-logic", "slp-trending-behaviour:3", "empty-search-rollout", "dc-realtime-sorting-config:bf21a9", "cart-master-ptype-based-deboosting-on-npr", "reduce-precision-set-byc", "mv-instant-enabled", "empty-search-trending-keyterms:enabled", "enable-blinkit-money", "dark-mode-rollout-enabled", "high-confident-pb-ranking", "use-butterfly-model", "pb-intelligent-scoring-version:v2", "use-product-sales-score", "new-text-match-qtp-scoring", "is_variant_compression_enabled", "is-primary-config-endpoint-enabled", "post-checkout-crystal-bff-migration-rollout-enabled", "track-order-v2-aerobar-version-rollout", "consumer-app-wishlist-feature-enabled", "butterfly-spellcorrect-ab", "use-butterfly-spellcorrect", "location-permission-popup-v2-enabled", "enable-pharma-flow", "location-autosuggest-backend-enabled", "consumer-app-web-view-auth-token-flow-enabled", "use-pds-product-id-products", "consumer-app-feeding-india-impact-page-rollout-enabled", "consumer-app-android-compose-framework-enabled", "far_away_flow_profile", "far_away_flow_cart"], "segment_type": ["high_aov_segment_dump_new_def", "lux_brands_segment", "ntc_icecream", "baby_care_segment", "school_cohort_low", "school_cohort", "ntc_toys_and_games", "stationary_buyers", "new_potential", "non_premium_ull_flag", "health_fitness", "ads_diy_grocery_buy", "repellents_user", "chocolate_munchies_biscuit_premium", "ads_cat_surfer", "ads_kids", "ads_male", "segment_hyderabad_non_fnv_v", "dog_search_not_transact", "ads_dog_surfer", "grocery_user", "stationery_need", "cat_new_potential", "diswashing_gel_sampling", "dog_new_potential", "munchies_users", "hpstationary_auto", "maxkleen_auto", "milk_drink_mix", "ASBL_Flyer", "personalisation_non_vegetarian_cohort_high", "personalisation_kids_cohort_high", "segment_single_mode_realtime"], "session_launch_source": "#-NA", "session_uuid": "f2b4cb9b-a311-4565-a8a1-9e9cfab682a5", "user_experiment_buckets": ["RANDOM#bucket1", "INCREASING_ROLLOUT#bucket2", "DECREASING_ROLLOUT#bucket2"], "user_type": "ACTIVE"}, "url": "", "user_agent": "&source=android_market&version=14&device_manufacturer=vivo&device_brand=vivo&device_model=V2158&app_version=17.47.0&app_type=blinkit_android&sdk_version=34&network_type=WIFI&user_lang=en&kmm_identifier=null", "user_id": "69628044"}