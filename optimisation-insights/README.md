
# Performance Optimization Insights

This directory contains benchmarking results and profiling data for the flash-gateway event transformation performance analysis.

## 📁 Files Overview

- **`benchmark_analysis_report.md`** - Comprehensive analysis comparing custom vs generic transformers
- **`profiling_guide.md`** - Complete guide on how to analyze `.prof` files
- **`sample_events/`** - Contains production event samples used for benchmarking
- **CPU/Memory profiles** - Located in `internal/transformers/jumbo-v2-event-consumer/*.prof`

## 🚀 Quick Start

### 1. Run Benchmarks

```bash
# Direct comparison of custom vs generic transformers
make bench-compare

# Test performance across different payload complexities
make bench-complexity

# Generate comprehensive report with profiling data
make bench-report

# Memory allocation analysis
make bench-memory
```

### 2. Analyze Profiles

```bash
# Navigate to profile directory
cd internal/transformers/jumbo-v2-event-consumer

# View CPU usage breakdown
go tool pprof -top cpu.prof

# View memory allocation breakdown  
go tool pprof -top mem.prof

# Interactive web interface (after installing graphviz)
go tool pprof -http=localhost:8080 cpu.prof
```

### 3. Install Required Tools

For visual profiling (graphs and flame charts):

```bash
# macOS
brew install graphviz

# Ubuntu/Debian
sudo apt-get install graphviz

# CentOS/RHEL
sudo yum install graphviz
```

## 📊 Key Performance Results

### Summary

| Metric | Custom Transformer | Generic Transformer | Improvement |
|--------|-------------------|---------------------|-------------|
| **Speed** | 45,690 ns/op | 186,179 ns/op | **4.07x faster** |
| **Memory** | 58,471 B/op | 165,430 B/op | **2.83x less** |
| **Allocations** | 542 allocs/op | 1,945 allocs/op | **3.59x fewer** |

### Production Impact

- **75% reduction in CPU usage** for `product_image_shown_events`
- **65% reduction in memory allocations**
- **Significant reduction in GC pressure**
- **~4x increase in throughput capacity**

## 🔍 Profiling Analysis

### CPU Profile Insights

From the CPU profile analysis:

1. **System Overhead**: 45% of time spent in runtime/system calls
2. **Your Function**: `getProductImageShownEvents` accounts for 26% of CPU time
3. **Protobuf Processing**: 17% of time spent in protobuf unmarshaling
4. **JSON Operations**: JSON processing is efficient with jsoniter

### Memory Profile Insights

From the memory profile analysis:

1. **Reflection Cost**: 30% of memory allocations from `reflect.New`
2. **Protobuf Overhead**: 51% of memory used by protobuf operations
3. **JSON Efficiency**: Only 19% of memory used by JSON operations
4. **Optimization Opportunity**: High reflection usage suggests room for improvement

## 🛠️ How to Analyze Profiles

### CPU Analysis

```bash
# Top CPU consumers
go tool pprof -top cpu.prof

# Interactive analysis
go tool pprof cpu.prof
(pprof) top
(pprof) list getProductImageShownEvents
(pprof) web  # Opens browser visualization
```

### Memory Analysis

```bash
# Top memory allocators
go tool pprof -top mem.prof

# Interactive analysis  
go tool pprof mem.prof
(pprof) top
(pprof) list getProductImageShownEvents
```

### Web Interface (Recommended)

```bash
# Start interactive web interface
go tool pprof -http=localhost:8080 cpu.prof

# Then visit http://localhost:8080 for:
# - Flame graphs
# - Call trees  
# - Interactive exploration
# - Export capabilities
```

## 📈 Benchmark Commands Reference

### Basic Benchmarks

```bash
# Single benchmark
go test -bench=BenchmarkCustomProductImageShownTransformer -run=^$ -v

# Compare both transformers
go test -bench="BenchmarkCustomProductImageShownTransformer$|BenchmarkGenericProductImageShownTransformer$" -run=^$ -v
```

### With Profiling

```bash
# Generate CPU and memory profiles
go test -bench=BenchmarkCustomProductImageShownTransformer -run=^$ -cpuprofile=cpu.prof -memprofile=mem.prof

# Extended benchmark time for more accurate profiling
go test -bench=BenchmarkCustomProductImageShownTransformer -run=^$ -benchtime=10s -cpuprofile=cpu.prof
```

### Memory-Focused Analysis

```bash
# Memory allocations per operation
go test -bench=BenchmarkCustomProductImageShownTransformer -run=^$ -benchmem

# Memory allocation patterns
go test -bench=BenchmarkMemoryAllocation -run=^$ -benchmem
```

## 🎯 Recommendations

### Immediate Actions

1. **Deploy Custom Transformers**: Prioritize `product_image_shown_events` for immediate CPU relief
2. **Monitor Production**: Track CPU and memory usage after deployment
3. **Expand Coverage**: Implement custom transformers for other high-volume events

### Performance Optimization

1. **Reduce Reflection**: The high `reflect.New` usage (30% of memory) can be optimized
2. **Object Pooling**: High allocation rates suggest potential for memory pools
3. **Protobuf Efficiency**: Consider caching or reusing protobuf objects

### Long-term Strategy

1. **Code Generation**: Automate custom transformer generation
2. **Continuous Benchmarking**: Integrate benchmarks into CI/CD pipeline
3. **Architecture Review**: Evaluate eliminating generic transformers entirely

## 🔧 Troubleshooting

### Profile Generation Issues

If you encounter profile errors:

```bash
# Clean and regenerate
rm -f *.prof
go test -bench=YourBenchmark -cpuprofile=cpu.prof -memprofile=mem.prof
```

### Graphviz Installation

For visual profiling capabilities:

```bash
# Verify installation
dot -V

# If missing, install via package manager
brew install graphviz  # macOS
sudo apt install graphviz  # Ubuntu
```

### Empty or Invalid Profiles

```bash
# Ensure benchmark runs long enough
go test -bench=YourBenchmark -benchtime=5s -cpuprofile=cpu.prof

# Check file permissions
ls -la *.prof
```

## 📚 Additional Resources

- **Benchmark Code**: `internal/transformers/jumbo-v2-event-consumer/benchmark_generic_vs_custom_test.go`
- **Custom Transformer**: `getProductImageShownEvents()` function
- **Generic Transformer**: `GetJumboV2FlattenedJSONMap()` function
- **Routing Logic**: `internal/handlers/claims.go`

For detailed analysis instructions, see `profiling_guide.md`.

---

**Next Steps**: Run `make bench-compare` to see the performance difference and `go tool pprof -http=localhost:8080 cpu.prof` to explore the detailed profiling data.
