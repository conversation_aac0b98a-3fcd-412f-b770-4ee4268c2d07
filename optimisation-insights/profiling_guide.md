
# Go Profiling Guide for .prof Files

This guide explains how to analyze the CPU and memory profile files generated by the benchmarks.

## Prerequisites

Go has built-in profiling tools. Make sure you have Go installed and you're in the project directory.

## Opening and Analyzing Profile Files

### 1. CPU Profile Analysis

```bash
# Navigate to the directory with the .prof files
cd internal/transformers/jumbo-v2-event-consumer

# View top CPU consumers
go tool pprof -top cpu.prof

# Interactive analysis
go tool pprof cpu.prof
```

#### CPU Profile Commands (Interactive Mode)

Once in interactive mode (`go tool pprof cpu.prof`), you can use these commands:

```bash
# Show top 10 functions by CPU usage
(pprof) top

# Show top 20 functions
(pprof) top20

# Show functions and their call graph
(pprof) list <function_name>

# Web interface (opens browser)
(pprof) web

# Generate SVG call graph
(pprof) svg > cpu_profile.svg

# Show specific function details
(pprof) list getProductImageShownEvents

# Exit
(pprof) exit
```

### 2. Memory Profile Analysis

```bash
# View top memory allocators
go tool pprof -top mem.prof

# Interactive analysis
go tool pprof mem.prof
```

#### Memory Profile Commands (Interactive Mode)

```bash
# Show top memory allocators
(pprof) top

# Show allocations by count vs size
(pprof) top -cum

# Show memory usage by function
(pprof) list <function_name>

# Generate web view
(pprof) web
```

### 3. Comparative Analysis

```bash
# Compare two profiles (useful for before/after optimization)
go tool pprof -base=old.prof new.prof

# Generate diff report
go tool pprof -top -diff_base=old.prof new.prof
```

## Understanding Profile Output

### CPU Profile Output Interpretation

From your recent CPU profile:

```
      flat  flat%   sum%        cum   cum%
     1.10s 45.08% 45.08%      1.10s 45.08%  runtime.kevent
     0.22s  9.02% 54.10%      0.22s  9.02%  runtime.pthread_cond_wait
     0.01s  0.41% 89.34%      0.64s 26.23%  getProductImageShownEvents
```

- **flat**: Time spent in this function alone
- **flat%**: Percentage of total time spent in this function
- **sum%**: Cumulative percentage up to this function
- **cum**: Cumulative time spent in this function and its callees
- **cum%**: Cumulative percentage including callees

### Memory Profile Output Interpretation

From your recent memory profile:

```
      flat  flat%   sum%        cum   cum%
    0.60GB 30.21% 30.21%     0.60GB 30.21%  reflect.New
    0.46GB 22.82% 53.03%     1.99GB 99.60%  getProductImageShownEvents
```

- **flat**: Memory allocated directly by this function
- **cum**: Total memory allocated by this function and its callees

## Key Performance Insights from Your Profiles

### CPU Profile Analysis

1. **Runtime Overhead**: 45% of CPU time is spent in `runtime.kevent` (system calls)
2. **Your Function**: `getProductImageShownEvents` accounts for 26.23% cumulative CPU time
3. **JSON Processing**: `jsoniter` functions are visible but not dominant
4. **Protobuf Unmarshaling**: 16.80% of time spent in protobuf unmarshaling

### Memory Profile Analysis

1. **Reflection Cost**: `reflect.New` consumes 30.21% of memory allocations
2. **Your Function Impact**: `getProductImageShownEvents` responsible for 99.60% cumulative allocations
3. **Protobuf Processing**: 51% of memory used by protobuf unmarshaling
4. **JSON Marshaling**: 18.78% of memory used by JSON operations

## Advanced Analysis Techniques

### 1. Flame Graphs

```bash
# Generate flame graph (requires go-torch or pprof web interface)
go tool pprof -http=:8080 cpu.prof
# Then visit http://localhost:8080 in your browser
```

### 2. Call Graph Analysis

```bash
# Generate call graph
go tool pprof -pdf cpu.prof > cpu_callgraph.pdf
go tool pprof -svg cpu.prof > cpu_callgraph.svg
```

### 3. Line-by-Line Analysis

```bash
# In interactive mode, examine specific functions
(pprof) list getProductImageShownEvents
```

This will show source code with performance annotations.

### 4. Memory Leak Detection

```bash
# For heap analysis (different from alloc_space)
go tool pprof -inuse_space mem.prof
```

## Benchmark-Specific Commands

### Generate Both CPU and Memory Profiles

```bash
# Single benchmark with both profiles
go test -bench=BenchmarkCustomProductImageShownTransformer -run=^$ -cpuprofile=cpu.prof -memprofile=mem.prof

# Compare custom vs generic
go test -bench="BenchmarkCustomProductImageShownTransformer|BenchmarkGenericProductImageShownTransformer" -run=^$ -cpuprofile=compare_cpu.prof -memprofile=compare_mem.prof
```

### Makefile Integration

You can use the provided Makefile targets:

```bash
# Generate profiles and run comparison
make bench-report

# Focus on memory analysis
make bench-memory
```

## Tips for Performance Optimization

Based on the profile analysis:

1. **Reduce Reflection**: The high `reflect.New` usage suggests opportunities to reduce reflection
2. **Optimize JSON Processing**: Consider more efficient JSON serialization approaches
3. **Protobuf Efficiency**: The protobuf unmarshaling is expensive - consider caching or reuse
4. **Memory Pools**: High allocation rates suggest potential for object pooling

## Troubleshooting

### Profile File Issues

If you get "gzip: invalid header" errors:

```bash
# Regenerate the profile
rm -f *.prof
go test -bench=YourBenchmark -cpuprofile=cpu.prof -memprofile=mem.prof
```

### Empty Profiles

If profiles appear empty:

```bash
# Ensure benchmark runs long enough
go test -bench=YourBenchmark -benchtime=10s -cpuprofile=cpu.prof
```

### Permission Issues

```bash
# Ensure you have write permissions in the directory
chmod 755 .
```

## Web Interface

For the most user-friendly analysis:

```bash
# Start web interface (modern go tool pprof)
go tool pprof -http=localhost:8080 cpu.prof

# Or for older versions
go tool pprof cpu.prof
(pprof) web
```

This opens an interactive web interface where you can:
- View flame graphs
- Explore call trees
- Compare different views (flat, cumulative, etc.)
- Export visualizations

## Integration with CI/CD

To track performance over time:

```bash
# Generate profiles in CI
go test -bench=. -cpuprofile=ci_cpu.prof -memprofile=ci_mem.prof

# Store profiles as artifacts for later analysis
# Compare against baseline profiles to detect regressions
```

The web interface provides the most intuitive way to explore performance bottlenecks and is highly recommended for detailed analysis.
