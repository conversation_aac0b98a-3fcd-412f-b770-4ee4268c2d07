# syntax = docker/dockerfile:1

########################################
## Build Stage
########################################
FROM public.ecr.aws/docker/library/golang:1.20.14-bookworm as builder

# add a label to clean up later
LABEL stage=intermediate

# setup the working directory
WORKDIR /go/src

# add netrc file to allow access to private github repo
COPY .netrc /root/.netrc

# install dependencies
ENV GO111MODULE=on
ENV GOPRIVATE=github.com/Zomato/*

COPY ./go.mod ./go.mod
COPY ./client/golang ./client/golang

# add source code
COPY . .
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod tidy && go mod download 

# build the source
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o flash-gateway-linux-amd64 -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn"

########################################
## Production Stage
########################################
FROM public.ecr.aws/zomato/zomato/base:v1

# set working directory
WORKDIR /root

# copy required files from builder
COPY --from=builder /go/src/flash-gateway-linux-amd64 ./flash-gateway-linux-amd64
COPY --from=builder /go/src/configs ./configs

CMD ["./flash-gateway-linux-amd64"]
