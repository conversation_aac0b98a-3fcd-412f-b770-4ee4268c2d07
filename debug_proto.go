package main

import (
	"fmt"
	"reflect"

	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/blinkit/consumer/impressionevents"
)

func main() {
	// Create an instance and inspect available methods
	pb := &impressionevents.ImpressionEvents{}
	props := pb.GetProperties()

	if props != nil {
		t := reflect.TypeOf(props)
		fmt.Println("Available methods on Properties:")
		for i := 0; i < t.NumMethod(); i++ {
			method := t.Method(i)
			if method.Name[:3] == "Get" {
				fmt.Printf("- %s\n", method.Name)
			}
		}
	}
}
