version: 0.2

env:
  variables:
    GITHUB_REPO_NAME: "flash-gateway"
    PHP_CLIENT_REPO_URI: "github.com/Zomato/event-gateway-service-client-php.git"
    ECR_REPOSITORY_URI: "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/zomato/flash-gateway"
    MOBY_BUILDKIT_URI: "public.ecr.aws/zomato/common/moby/buildkit:buildx-stable-1"
    DOCKER_BUILDKIT: 1

phases:
  install:
    # runtime-versions:
    #   docker: 18
    commands:
      - echo "Starting"
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc

  pre_build:
    commands:
      - # setting git config
      - git config --global user.name $GITHUB_BOT_NAME
      - git config --global user.email $GITHUB_BOT_EMAIL

      - IMAGE_TAG=dev
      #$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)

  build:
    commands:
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI
      - echo "building and pushing multiarch image"
      - make compile_protos
      # - docker build -t zomato/flash-gateway:$IMAGE_TAG .
      # - docker tag zomato/flash-gateway:dev $ECR_REPOSITORY_URI:$IMAGE_TAG
      # - docker push $ECR_REPOSITORY_URI:$IMAGE_TAG
      - docker buildx create --name multiarch --driver docker-container --driver-opt image=${MOBY_BUILDKIT_URI} --use
      - docker buildx build --platform=linux/amd64,linux/arm64 -t ${ECR_REPOSITORY_URI}:${IMAGE_TAG} --provenance=false --push .

  post_build:
    commands:
    #   ## pushing php client ###########################################################
    #   - cd $CODEBUILD_SRC_DIR
    #   - PHP_CLIENT_REPO_NAME=$(basename $PHP_CLIENT_REPO_URI .git)

    #  # cloning php client repository
    #   - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$PHP_CLIENT_REPO_URI

    #  # pushing latest changes
    #   - cd $PHP_CLIENT_REPO_NAME && git checkout master && git reset --hard origin/dev && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
    #   - git push -f origin master

      ### updating image definition ####################################################
      - cd $CODEBUILD_SRC_DIR
      - sed -i "s#<container-image>#$ECR_REPOSITORY_URI:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-preprod.json

artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-preprod.json"
