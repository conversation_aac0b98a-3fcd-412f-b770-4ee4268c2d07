version: 0.2

env:
  variables:
    GITHUB_REPO_NAME: "flash-gateway"
    GITHUB_API_BASE: "https://api.github.com/repos/Zomato/flash-gateway"
    PHP_CLIENT_REPO_URI: "github.com/Zomato/event-gateway-service-client-php.git"
    DOCKER_BUILDKIT: 1

phases:
  install:
    runtime-versions:
      docker: 18
    commands:
      - echo "Starting"
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc

  pre_build:
    commands:
      - # setting git config
      - git config --global user.name $GITHUB_BOT_NAME
      - git config --global user.email $GITHUB_BOT_EMAIL

      - echo "Logging in to Amazon ECR..."
      - $(aws ecr get-login --registry-id 711387117679 --region ap-south-1 --no-include-email)
      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - "export RELEASE_TAG=$(curl -H 'Authorization: token '\"$GITHUB_BOT_PAC\"'' \"$GITHUB_API_BASE\"/releases/latest | jq -r  '.tag_name')"
      - "export ARTIFACT_VERSION=$(echo $RELEASE_TAG | sed 's/v//g')"

  build:
    commands:
      - # run source code tests here
      - echo "building image"
      - make build
      - docker build -t zomato/flash-gateway:$IMAGE_TAG .
      - docker tag zomato/flash-gateway:$IMAGE_TAG $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG
      - docker push $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG
      - MANIFEST=$(aws ecr batch-get-image --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-ids imageTag=$IMAGE_TAG --query 'images[].imageManifest' --output text)
      - aws ecr put-image --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-tag $RELEASE_TAG --image-manifest "$MANIFEST"

  post_build:
    commands:
      ### updating image definition ####################################################
      - cd $CODEBUILD_SRC_DIR
      - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$RELEASE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-nugget-prod.json

artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-nugget-prod.json"
