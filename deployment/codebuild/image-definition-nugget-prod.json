{"services": [{"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-proto-conv-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-web-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-proto-conv-backend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-web-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-nugget-jumbo-consumer-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-nugget-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-jumbov2eventconsumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-jumbov2eventconsumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}], "serviceName": "flash-gateway", "appconfigEnvironment": "prod", "disableConfigInjection": true}